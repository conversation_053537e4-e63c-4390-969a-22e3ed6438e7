<?php
/**
 * <PERSON><PERSON> (Data Center Infrastructure Management) Add-on for WHMCS
 * 
 * This file has been refactored into multiple modules for better maintainability.
 * All functionality is now handled by dcim-main.php which includes the modular files:
 * 
 * - dcim-core.php       (Configuration, database setup, utilities)
 * - dcim-sidebar.php    (Navigation menu generation)
 * - dcim-ipam.php       (IP Address Management)
 * - dcim-locations.php  (Location management)
 * - dcim-servers.php    (Server management)
 * - dcim-switches.php   (Switch management)
 * - dcim-chassis.php    (Chassis management)
 * - dcim-dashboard.php  (Dashboard views and rack visualization)
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    2.0.0 (Modular)
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Include the new modular main file that handles all functionality
require_once __DIR__ . '/dcim-main.php';

// Note: All required functions (dcim_config, dcim_activate, dcim_deactivate, dcim_output, etc.)
// are now defined in dcim-main.php and the included module files.
// WHMCS will automatically call these functions as needed. 