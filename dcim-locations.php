<?php

/**
 * DCIM Location Management Module
 * 
 * This file contains all functions related to managing data center locations
 * and their associated racks in the DCIM system.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, configurations)
 * - dcim-sidebar.php (navigation interface)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * Manage data center locations with modern interface
 * 
 * Handles location creation, editing, deletion and displays a modern
 * interface for managing data center locations with their associated
 * contact information, power capacity, and rack counts.
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_manage_locations($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Handle form submissions
    if ($_POST['action'] == 'add_location') {
        try {
            Capsule::table('dcim_locations')->insert([
                'name' => $_POST['name'],
                'address' => $_POST['address'],
                'city' => $_POST['city'],
                'country' => $_POST['country'],
                'contact_name' => $_POST['contact_name'],
                'contact_email' => $_POST['contact_email'],
                'contact_phone' => $_POST['contact_phone'],
                'total_power_capacity' => $_POST['total_power_capacity'] ?: 0,
                'power_unit' => $_POST['power_unit'],
                'notes' => $_POST['notes'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo '<div class="alert alert-success">Location added successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error adding location: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_locations')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Location deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting location: ' . $e->getMessage() . '</div>';
        }
    }
    
    try {
        $locations = Capsule::table('dcim_locations')->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching locations: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Location Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add location form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;" id="addLocationForm">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Location</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_location">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location Name *</label>';
    echo '<input type="text" name="name" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">City</label>';
    echo '<input type="text" name="city" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Country</label>';
    echo '<input type="text" name="country" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Name</label>';
    echo '<input type="text" name="contact_name" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Email</label>';
    echo '<input type="email" name="contact_email" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Phone</label>';
    echo '<input type="text" name="contact_phone" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Capacity</label>';
    echo '<input type="number" name="total_power_capacity" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Unit</label>';
    echo '<select name="power_unit" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="Watts">Watts</option>';
    echo '<option value="Kilowatts">Kilowatts</option>';
    echo '<option value="Amps">Amps</option>';
    echo '</select>';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Address</label>';
    echo '<textarea name="address" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Notes</label>';
    echo '<textarea name="notes" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Add Location';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing locations
    if (count($locations) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Locations</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Contact</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Power</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Racks</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($locations as $location) {
            $rack_count = 0;
            try {
                $rack_count = Capsule::table('dcim_racks')->where('location_id', $location->id)->count();
            } catch (Exception $e) {
                error_log("DCIM: Error counting racks - " . $e->getMessage());
            }
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($location->name) . '</div>';
            if ($location->city) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($location->city . ', ' . $location->country) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($location->contact_name) {
                echo htmlspecialchars($location->contact_name);
                if ($location->contact_email) {
                    echo '<br><span style="font-size: 12px;">' . htmlspecialchars($location->contact_email) . '</span>';
                }
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($location->total_power_capacity > 0) {
                echo number_format($location->total_power_capacity) . ' ' . $location->power_unit;
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . $rack_count . '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewRacks(' . $location->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '<button onclick="deleteLocation(' . $location->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function viewRacks(locationId) {
        window.location.href = "' . $modulelink . '&location_id=" + locationId;
    }
    
    function showAddForm() {
        document.getElementById("addLocationForm").scrollIntoView();
    }
    </script>';
}

/**
 * Manage racks with modern interface
 * 
 * Provides a comprehensive interface for managing server racks within
 * data center locations. Includes rack creation, editing, deletion,
 * capacity tracking, and utilization visualization.
 * 
 * @param string $modulelink The WHMCS module link for navigation
 */
function dcim_manage_racks($modulelink) {
    $location_filter = $_GET['location_id'] ?? null;
    
    // Handle form submissions
    if ($_POST['action'] == 'add_rack') {
        try {
            Capsule::table('dcim_racks')->insert([
                'location_id' => $_POST['location_id'],
                'name' => $_POST['name'],
                'row' => $_POST['row'],
                'position' => $_POST['position'],
                'units' => $_POST['units'],
                'power_capacity' => $_POST['power_capacity'],
                'pdu_a' => $_POST['pdu_a'],
                'pdu_b' => $_POST['pdu_b'],
                'notes' => $_POST['notes'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo '<div class="alert alert-success">Rack added successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error adding rack: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_racks')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Rack deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting rack: ' . $e->getMessage() . '</div>';
        }
    }
    
    try {
        $locations = Capsule::table('dcim_locations')->get();
        
        $racks_query = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name');
        
        if ($location_filter) {
            $racks_query->where('dcim_racks.location_id', $location_filter);
        }
        
        $racks = $racks_query->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
        $racks = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $location_filter);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Rack Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add rack form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;" id="addRackForm">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Rack</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_rack">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location *</label>';
    echo '<select name="location_id" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="">Select Location</option>';
    foreach ($locations as $location) {
        $selected = ($location_filter == $location->id) ? 'selected' : '';
        echo '<option value="' . $location->id . '" ' . $selected . '>' . htmlspecialchars($location->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Rack Name *</label>';
    echo '<input type="text" name="name" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Row</label>';
    echo '<input type="text" name="row" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Position</label>';
    echo '<input type="text" name="position" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Units</label>';
    echo '<input type="number" name="units" value="42" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Capacity (W)</label>';
    echo '<input type="number" name="power_capacity" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">PDU A</label>';
    echo '<input type="text" name="pdu_a" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">PDU B</label>';
    echo '<input type="text" name="pdu_b" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Notes</label>';
    echo '<textarea name="notes" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Add Rack';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing racks
    if (count($racks) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Racks</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Capacity</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Utilization</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($racks as $rack) {
            $server_count = 0;
            $used_units = 0;
            try {
                $server_count = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->count();
                $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->sum('unit_size');
            } catch (Exception $e) {
                error_log("DCIM: Error counting servers - " . $e->getMessage());
            }
            
            $utilization = $rack->units > 0 ? round(($used_units / $rack->units) * 100, 1) : 0;
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($rack->name) . '</div>';
            if ($rack->row || $rack->position) {
                echo '<div style="font-size: 12px; color: #6b7280;">Row ' . htmlspecialchars($rack->row) . ', Pos ' . htmlspecialchars($rack->position) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($rack->location_name) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($rack->row . ' / ' . $rack->position) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . $rack->units . 'U</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; align-items: center; gap: 8px;">';
            echo '<div style="flex: 1; background: #f3f4f6; height: 8px; border-radius: 4px; overflow: hidden;">';
            echo '<div style="width: ' . $utilization . '%; height: 100%; background: ' . ($utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
            echo '</div>';
            echo '<span style="font-size: 12px; color: #6b7280;">' . $utilization . '%</span>';
            echo '</div>';
            echo '<div style="font-size: 12px; color: #9ca3af; margin-top: 4px;">' . $used_units . '/' . $rack->units . 'U used</div>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewRack(' . $rack->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '<button onclick="manageServers(' . $rack->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-server"></i> Servers';
            echo '</button>';
            echo '<button onclick="deleteRack(' . $rack->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function viewRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function manageServers(rackId) {
        window.location.href = "' . $modulelink . '&action=servers&rack_id=" + rackId;
    }
    
    function deleteRack(rackId) {
        if (confirm("Are you sure you want to delete this rack and all its servers?")) {
            window.location.href = "' . $modulelink . '&action=racks&delete=" + rackId;
        }
    }
    
    function showAddForm() {
        document.getElementById("addRackForm").scrollIntoView();
    }
    
    // Search functionality for rack table
    if (document.getElementById("rackSearch")) {
        document.getElementById("rackSearch").addEventListener("input", function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll("tbody tr");
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = "";
                } else {
                    row.style.display = "none";
                }
            });
        });
    }
    </script>';
}

/**
 * Get rack statistics for a specific location
 * 
 * @param int $location_id The location ID to get stats for
 * @return array Array containing rack statistics
 */
function dcim_get_location_rack_stats($location_id) {
    try {
        $total_racks = Capsule::table('dcim_racks')
            ->where('location_id', $location_id)
            ->count();
            
        $total_units = Capsule::table('dcim_racks')
            ->where('location_id', $location_id)
            ->sum('units');
            
        $used_units = Capsule::table('dcim_servers')
            ->join('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->where('dcim_racks.location_id', $location_id)
            ->sum('dcim_servers.unit_size');
            
        $total_servers = Capsule::table('dcim_servers')
            ->join('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->where('dcim_racks.location_id', $location_id)
            ->count();
            
        return [
            'total_racks' => $total_racks,
            'total_units' => $total_units,
            'used_units' => $used_units,
            'available_units' => $total_units - $used_units,
            'utilization_percent' => $total_units > 0 ? round(($used_units / $total_units) * 100, 1) : 0,
            'total_servers' => $total_servers
        ];
    } catch (Exception $e) {
        error_log("DCIM: Error getting location rack stats - " . $e->getMessage());
        return [
            'total_racks' => 0,
            'total_units' => 0,
            'used_units' => 0,
            'available_units' => 0,
            'utilization_percent' => 0,
            'total_servers' => 0
        ];
    }
}

/**
 * Get a list of all locations with basic information
 * 
 * @return array Array of location objects
 */
function dcim_get_all_locations() {
    try {
        return Capsule::table('dcim_locations')->orderBy('name')->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching locations - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get location details by ID
 * 
 * @param int $location_id The location ID
 * @return object|null Location object or null if not found
 */
function dcim_get_location_by_id($location_id) {
    try {
        return Capsule::table('dcim_locations')->where('id', $location_id)->first();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching location - " . $e->getMessage());
        return null;
    }
}

/**
 * Get racks for a specific location
 * 
 * @param int $location_id The location ID
 * @return array Array of rack objects
 */
function dcim_get_location_racks($location_id) {
    try {
        return Capsule::table('dcim_racks')
            ->where('location_id', $location_id)
            ->orderBy('row')
            ->orderBy('position')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching location racks - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Validate location data
 * 
 * @param array $data Location data to validate
 * @return array Array with 'valid' boolean and 'errors' array
 */
function dcim_validate_location_data($data) {
    $errors = [];
    
    if (empty($data['name'])) {
        $errors[] = 'Location name is required';
    }
    
    if (!empty($data['contact_email']) && !filter_var($data['contact_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email address';
    }
    
    if (!empty($data['total_power_capacity']) && !is_numeric($data['total_power_capacity'])) {
        $errors[] = 'Power capacity must be a number';
    }
    
    return [
        'valid' => count($errors) == 0,
        'errors' => $errors
    ];
}

/**
 * Create a new location
 * 
 * @param array $data Location data
 * @return array Result array with success/error information
 */
function dcim_create_location($data) {
    try {
        $validation = dcim_validate_location_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $location_id = Capsule::table('dcim_locations')->insertGetId([
            'name' => $data['name'],
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'country' => $data['country'] ?? null,
            'contact_name' => $data['contact_name'] ?? null,
            'contact_email' => $data['contact_email'] ?? null,
            'contact_phone' => $data['contact_phone'] ?? null,
            'total_power_capacity' => $data['total_power_capacity'] ?? 0,
            'power_unit' => $data['power_unit'] ?? 'Watts',
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'] ?? 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        return ['success' => true, 'location_id' => $location_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating location - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update an existing location
 * 
 * @param int $location_id The location ID
 * @param array $data Updated location data
 * @return array Result array with success/error information
 */
function dcim_update_location($location_id, $data) {
    try {
        $validation = dcim_validate_location_data($data);
        if (!$validation['valid']) {
            return ['success' => false, 'errors' => $validation['errors']];
        }
        
        $updateData = [
            'name' => $data['name'],
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'country' => $data['country'] ?? null,
            'contact_name' => $data['contact_name'] ?? null,
            'contact_email' => $data['contact_email'] ?? null,
            'contact_phone' => $data['contact_phone'] ?? null,
            'total_power_capacity' => $data['total_power_capacity'] ?? 0,
            'power_unit' => $data['power_unit'] ?? 'Watts',
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if (isset($data['status'])) {
            $updateData['status'] = $data['status'];
        }
        
        Capsule::table('dcim_locations')
            ->where('id', $location_id)
            ->update($updateData);
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error updating location - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Delete a location and all associated racks and servers
 * 
 * @param int $location_id The location ID to delete
 * @return array Result array with success/error information
 */
function dcim_delete_location($location_id) {
    try {
        // Note: With proper foreign key constraints, cascading deletes should handle this
        // But we'll be explicit to ensure data integrity
        
        // First, delete all servers in racks at this location
        $racks = Capsule::table('dcim_racks')->where('location_id', $location_id)->pluck('id');
        if (count($racks) > 0) {
            Capsule::table('dcim_servers')->whereIn('rack_id', $racks)->delete();
        }
        
        // Then delete all racks at this location
        Capsule::table('dcim_racks')->where('location_id', $location_id)->delete();
        
        // Finally delete the location
        Capsule::table('dcim_locations')->where('id', $location_id)->delete();
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting location - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

?> 