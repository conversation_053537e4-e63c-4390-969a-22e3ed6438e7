<?php
/**
 * Test script to debug subnet modal functionality
 */

// Include WHMCS environment
require_once __DIR__ . '/../../init.php';
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-ipam.php';

use WHMCS\Database\Capsule;

// Set content type
header('Content-Type: text/html; charset=UTF-8');

echo '<html><head><title>Subnet Modal Debug</title></head><body>';
echo '<h1>Subnet Modal Debug Test</h1>';

try {
    // First, check if we have any subnets
    $subnets = Capsule::table('dcim_subnets')->get();
    echo '<h2>Available Subnets:</h2>';
    
    if (count($subnets) > 0) {
        echo '<ul>';
        foreach ($subnets as $subnet) {
            echo '<li>ID: ' . $subnet->id . ' - ' . htmlspecialchars($subnet->subnet) . '</li>';
        }
        echo '</ul>';
        
        // Test the AJAX endpoint with the first subnet
        $test_subnet = $subnets[0];
        echo '<h2>Testing AJAX endpoint for subnet ID: ' . $test_subnet->id . '</h2>';
        
        // Simulate the AJAX call
        $_GET['subnet_id'] = $test_subnet->id;
        
        // Capture output
        ob_start();
        dcim_handle_subnet_details_ajax($test_subnet->id);
        $ajax_output = ob_get_clean();
        
        echo '<h3>AJAX Response:</h3>';
        echo '<pre>' . htmlspecialchars($ajax_output) . '</pre>';
        
        // Try to decode the JSON
        $response = json_decode($ajax_output, true);
        if ($response) {
            echo '<h3>Parsed JSON Response:</h3>';
            echo '<pre>' . print_r($response, true) . '</pre>';
        } else {
            echo '<p style="color: red;">Failed to parse JSON response!</p>';
            echo '<p>JSON Error: ' . json_last_error_msg() . '</p>';
        }
        
    } else {
        echo '<p>No subnets found in database. Please create a subnet first.</p>';
        
        // Show table structure
        echo '<h2>Database Table Structure:</h2>';
        $columns = Capsule::schema()->getColumnListing('dcim_subnets');
        echo '<pre>' . print_r($columns, true) . '</pre>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
}

echo '</body></html>';
?>
