<?php

/**
 * <PERSON><PERSON> IPAM (IP Address Management) Module
 * 
 * This file contains all functions related to managing IP addresses and subnets
 * in the DCIM system, including subnet creation, IP allocation, and network management.
 * 
 * Dependencies:
 * - dcim-core.php (database tables, IPAM utility functions)
 * - dcim-sidebar.php (navigation interface)
 * 
 * <AUTHOR> System
 * @version 1.0
 */

// Ensure this file is only included within WHMCS context
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

// Include required dependencies
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-sidebar.php';

/**
 * IPAM Dashboard - Main entry point for IPAM functionality
 */
function dcim_ipam_dashboard($modulelink) {
    $subaction = $_GET['subaction'] ?? 'dashboard';

    // Handle AJAX requests
    if ($subaction === 'subnet_details' && !empty($_GET['subnet_id'])) {
        dcim_handle_subnet_details_ajax($_GET['subnet_id']);
        return;
    }

    if ($subaction === 'divide_subnet' && !empty($_POST['subnet_id']) && !empty($_POST['new_prefix'])) {
        dcim_handle_divide_subnet_ajax($_POST['subnet_id'], $_POST['new_prefix']);
        return;
    }

    if ($subaction === 'generate_ips' && !empty($_POST['subnet_id'])) {
        dcim_handle_generate_ips_ajax($_POST['subnet_id']);
        return;
    }

    if ($subaction === 'delete_subnet_check' && !empty($_GET['subnet_id'])) {
        dcim_handle_delete_subnet_check_ajax($_GET['subnet_id']);
        return;
    }

    if ($subaction === 'regenerate_subnets' && !empty($_POST['subnet_id'])) {
        dcim_handle_regenerate_subnets_ajax($_POST['subnet_id']);
        return;
    }

    switch ($subaction) {
        case 'subnets':
            dcim_manage_subnets($modulelink);
            break;
        case 'ips':
            dcim_manage_ips($modulelink);
            break;
        case 'allocation':
            dcim_ip_allocation($modulelink);
            break;
        default:
            dcim_ipam_overview($modulelink);
            break;
    }
}

/**
 * Handle AJAX request for subnet details
 */
function dcim_handle_subnet_details_ajax($subnet_id) {
    header('Content-Type: application/json');

    try {
        $subnet = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->where('dcim_subnets.id', $subnet_id)
            ->first();

        if (!$subnet) {
            echo json_encode(['success' => false, 'error' => 'Subnet not found']);
            exit;
        }

        // Get subnet status and available actions
        $status = dcim_get_subnet_status($subnet_id);

        // Get child subnets for hierarchy display
        $children = Capsule::table('dcim_subnets')
            ->where('parent_id', $subnet_id)
            ->orderBy('network')
            ->get();

        // Get IP addresses count
        $ip_stats = Capsule::table('dcim_ip_addresses')
            ->selectRaw('COUNT(*) as total, SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned')
            ->where('subnet_id', $subnet_id)
            ->first();

        echo json_encode([
            'success' => true,
            'subnet' => $subnet,
            'status' => $status,
            'children' => $children,
            'ip_stats' => $ip_stats
        ]);
        exit;
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        exit;
    }
}

/**
 * Handle AJAX request for subnet division
 */
function dcim_handle_divide_subnet_ajax($subnet_id, $new_prefix) {
    header('Content-Type: application/json');

    $result = dcim_divide_subnet($subnet_id, intval($new_prefix));
    echo json_encode($result);
    exit;
}

/**
 * Handle AJAX request for IP generation
 */
function dcim_handle_generate_ips_ajax($subnet_id) {
    header('Content-Type: application/json');

    $result = dcim_generate_subnet_ips_safe($subnet_id);
    echo json_encode($result);
    exit;
}

/**
 * Handle AJAX request for subnet deletion check
 */
function dcim_handle_delete_subnet_check_ajax($subnet_id) {
    header('Content-Type: application/json');

    $status = dcim_get_subnet_status($subnet_id);
    echo json_encode(['success' => true, 'status' => $status]);
    exit;
}

/**
 * Handle AJAX request for subnet regeneration
 */
function dcim_handle_regenerate_subnets_ajax($subnet_id) {
    header('Content-Type: application/json');

    $result = dcim_regenerate_child_subnets($subnet_id);
    echo json_encode($result);
    exit;
}

/**
 * IPAM Overview Dashboard
 */
function dcim_ipam_overview($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    dcim_force_create_ipam_tables();
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IPAM Dashboard</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Address Management Overview</h2>';
    echo '<p>Welcome to the IPAM module. Use the sidebar to navigate to different sections.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Subnet Management Interface
 */
function dcim_manage_subnets($modulelink) {
    // Force table creation if they don't exist or have wrong schema
    if (!dcim_force_create_ipam_tables()) {
        echo '<div class="alert alert-danger">Error: Could not create database tables. Please check your database permissions.</div>';
        return;
    }
    
    // Handle form submissions
    if (isset($_POST['subnet_action']) && $_POST['subnet_action'] == 'create_subnet') {
        $result = dcim_create_subnet($_POST);
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet created successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($result['error']) . '</div>';
        }
    }

    if (isset($_POST['subnet_action']) && $_POST['subnet_action'] === 'delete_subnet' && !empty($_POST['subnet_id'])) {
        $result = dcim_delete_subnet_safe($_POST['subnet_id']);
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet deleted successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($result['error']) . '</div>';
        }
    }
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using professional Unicode symbols */
    .fas.fa-sitemap::before { content: "⚡"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-eye::before { content: "◔"; }
    .fas.fa-trash::before { content: "✖"; }
    .fas.fa-search::before { content: "🔍"; }
    .fas.fa-filter::before { content: "⚙"; }
    </style>';
    
    try {
        // Check if the table has the expected columns
        $columns = Capsule::schema()->getColumnListing('dcim_subnets');

        if (!in_array('subnet', $columns)) {
            echo '<div class="alert alert-warning">';
            echo '<h4>Database Schema Update Required</h4>';
            echo '<p>Your DCIM database needs to be updated to support the new IPAM features.</p>';
            echo '<p><a href="dcim-migrate-tables.php" class="btn btn-primary">Run Database Migration</a></p>';
            echo '</div>';
            $subnets = collect([]);
        } else {
            // Get subnets with location information
            $subnets = Capsule::table('dcim_subnets')
                ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
                ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
                ->orderBy('dcim_subnets.subnet')
                ->get();
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h4>Database Error</h4>';
        echo '<p>Error fetching subnets: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>This usually means the database schema needs to be updated.</p>';
        echo '<p><a href="dcim-migrate-tables.php" class="btn btn-primary">Run Database Migration</a></p>';
        echo '</div>';
        $subnets = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">Subnets Management</div>';
    echo '<div style="font-size: 12px; color: #6b7280;">Last updated: ' . date('g:i:s A') . '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="showAddSubnetModal()" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px; background: #6366f1; color: white; padding: 8px 16px; border-radius: 6px;"><i class="fas fa-plus"></i> Add New Subnet</button>';
    echo '</div>';
    echo '</div>';
    
    // Filters and search
    echo '<div style="padding: 0 32px; margin-bottom: 16px;">';
    echo '<div style="display: flex; gap: 16px; align-items: center;">';
    echo '<select style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option>All Status</option>';
    echo '<option>Available</option>';
    echo '<option>Parent of Allocated Subnet</option>';
    echo '</select>';
    echo '<select style="padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option>All Locations</option>';
    echo '</select>';
    echo '<div style="position: relative; margin-left: auto;">';
    echo '<input type="text" placeholder="Search subnets..." style="padding: 8px 12px 8px 36px; border: 1px solid #d1d5db; border-radius: 6px; width: 250px;">';
    echo '<i class="fas fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #9ca3af;"></i>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Subnets table
    if (count($subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">SUBNET</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">STATUS</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">DATA CENTER</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">CATEGORY</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">NOTE</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">ACTIONS</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($subnets as $subnet) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->subnet) . '</div>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            if ($subnet->status === 'Available') {
                echo '<span style="background: #dcfce7; color: #166534; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"><i class="fas fa-check-circle" style="margin-right: 4px;"></i>Available</span>';
            } else {
                echo '<span style="background: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;"><i class="fas fa-exclamation-circle" style="margin-right: 4px;"></i>' . htmlspecialchars($subnet->status) . '</span>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($subnet->location_name) {
                echo htmlspecialchars($subnet->location_name);
            } elseif ($subnet->city && $subnet->country) {
                echo htmlspecialchars($subnet->city . ', ' . $subnet->country);
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($subnet->subnet_type) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . (htmlspecialchars($subnet->note) ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnetDetails(' . $subnet->id . ')" style="background: #f3f4f6; border: none; padding: 6px 8px; border-radius: 4px; cursor: pointer;" title="View Details"><i class="fas fa-eye"></i></button>';
            echo '<button onclick="deleteSubnet(' . $subnet->id . ')" style="background: #fef2f2; border: none; padding: 6px 8px; border-radius: 4px; cursor: pointer; color: #dc2626;" title="Delete"><i class="fas fa-trash"></i></button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
        
        // Pagination
        echo '<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px;">';
        echo '<div style="color: #6b7280; font-size: 14px;">Showing 1 to ' . count($subnets) . ' of ' . count($subnets) . ' subnets</div>';
        echo '<div style="display: flex; gap: 8px;">';
        echo '<button style="padding: 8px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Previous</button>';
        echo '<button style="padding: 8px 12px; border: 1px solid #6366f1; background: #6366f1; color: white; border-radius: 6px; cursor: pointer;">1</button>';
        echo '<button style="padding: 8px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Next</button>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div style="text-align: center; padding: 64px; color: #6b7280;">';
        echo '<i class="fas fa-sitemap" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>';
        echo '<h3 style="margin: 0 0 8px 0;">No subnets found</h3>';
        echo '<p style="margin: 0;">Create your first subnet to get started with IP address management.</p>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Add New Subnet Modal
    dcim_render_add_subnet_modal($modulelink);

    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    dcim_render_subnet_javascript($modulelink);
}

/**
 * Render the Add New Subnet Modal
 */
function dcim_render_add_subnet_modal($modulelink) {
    // Get data centers from database
    $datacenters = [];
    try {
        $datacenters = Capsule::table('dcim_locations')
            ->where('status', 'active')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching data centers - " . $e->getMessage());
    }

    echo '<div id="addSubnetModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">';
    echo '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">';

    // Modal header
    echo '<div style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">';
    echo '<div style="display: flex; justify-content: space-between; align-items: center;">';
    echo '<h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add New Subnet</h2>';
    echo '<button onclick="closeAddSubnetModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>';
    echo '</div>';
    echo '</div>';

    // Modal form
    echo '<form method="post" style="padding: 0 24px 24px 24px;">';
    echo '<input type="hidden" name="action" value="ipam">';
    echo '<input type="hidden" name="subaction" value="subnets">';
    echo '<input type="hidden" name="subnet_action" value="create_subnet">';

    // IP Version
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">IP Version *</label>';
    echo '<select name="ip_version" required style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option value="IPv4">IPv4</option>';
    echo '<option value="IPv6">IPv6</option>';
    echo '</select>';
    echo '</div>';

    // Subnet (CIDR notation)
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Subnet (CIDR notation) *</label>';
    echo '<input type="text" name="subnet" placeholder="e.g., ***********/24" required style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px;">';
    echo '</div>';

    // Data Center Selection
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Data Center</label>';
    echo '<select name="location_id" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">';
    echo '<option value="">-- Select Data Center --</option>';

    foreach ($datacenters as $datacenter) {
        $location_display = htmlspecialchars($datacenter->name);
        if ($datacenter->city && $datacenter->country) {
            $location_display .= ' (' . htmlspecialchars($datacenter->city . ', ' . $datacenter->country) . ')';
        }
        echo '<option value="' . $datacenter->id . '">' . $location_display . '</option>';
    }

    echo '</select>';
    echo '</div>';

    // Public Subnet
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Public Subnet</label>';
    echo '<div style="display: flex; gap: 16px;">';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="is_public" value="1" style="margin: 0;">';
    echo '<span>Yes</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="is_public" value="0" checked style="margin: 0;">';
    echo '<span>No</span>';
    echo '</label>';
    echo '</div>';
    echo '</div>';

    // Subnet Type
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Subnet Type</label>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Root" checked style="margin: 0;">';
    echo '<span>Root</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Customer" style="margin: 0;">';
    echo '<span>Customer</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Management" style="margin: 0;">';
    echo '<span>Management</span>';
    echo '</label>';
    echo '<label style="display: flex; align-items: center; gap: 6px; cursor: pointer;">';
    echo '<input type="radio" name="subnet_type" value="Transit" style="margin: 0;">';
    echo '<span>Transit</span>';
    echo '</label>';
    echo '</div>';
    echo '</div>';

    // Note
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Note</label>';
    echo '<textarea name="note" placeholder="Optional note for this subnet..." style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; min-height: 80px; resize: vertical;"></textarea>';
    echo '</div>';

    // Modal footer
    echo '<div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb;">';
    echo '<button type="button" onclick="closeAddSubnetModal()" style="padding: 10px 20px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="padding: 10px 20px; border: none; background: #6366f1; color: white; border-radius: 6px; cursor: pointer;">Add Subnet</button>';
    echo '</div>';

    echo '</form>';
    echo '</div>';
    echo '</div>';
}

/**
 * Render JavaScript functions for subnet management
 */
function dcim_render_subnet_javascript($modulelink) {
    echo '<script>
    function showAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "block";
    }

    function closeAddSubnetModal() {
        document.getElementById("addSubnetModal").style.display = "none";
    }

    function viewSubnetDetails(subnetId) {
        // Fetch subnet details via AJAX and show modal
        fetch("' . $modulelink . '&action=ipam&subaction=subnet_details&subnet_id=" + subnetId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSubnetDetailsModal(data);
                } else {
                    alert("Error loading subnet details: " + data.error);
                }
            })
            .catch(error => {
                console.error("Error:", error);
                alert("Error loading subnet details");
            });
    }

    function showSubnetDetailsModal(data) {
        var subnet = data.subnet;
        var status = data.status;
        var children = data.children || [];
        var ipStats = data.ip_stats || {total: 0, assigned: 0};

        var modal = document.createElement("div");
        modal.id = "subnetDetailsModal";
        modal.style.cssText = "display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;";

        // Build hierarchy display
        var hierarchyHtml = "";
        if (children.length > 0) {
            hierarchyHtml += "<div style=\\"font-size: 14px; font-weight: 500; margin-bottom: 8px;\\">Parent Subnet:</div>";
            hierarchyHtml += "<div style=\\"margin-left: 16px; margin-bottom: 12px;\\">";
            hierarchyHtml += "<div><i class=\\"fas fa-sitemap\\" style=\\"margin-right: 8px;\\"></i>" + subnet.subnet + " <span style=\\"color: #f59e0b;\\">Parent of " + children.length + " child subnets</span></div>";
            hierarchyHtml += "</div>";
            hierarchyHtml += "<div style=\\"font-size: 14px; font-weight: 500; margin-bottom: 8px;\\">Child Subnets:</div>";
            hierarchyHtml += "<div style=\\"margin-left: 32px; max-height: 200px; overflow-y: auto;\\">";
            for (var i = 0; i < children.length; i++) {
                hierarchyHtml += "<div style=\\"margin-bottom: 4px;\\"><i class=\\"fas fa-network-wired\\" style=\\"margin-right: 8px; color: #6b7280;\\"></i>" + children[i].subnet + " <span style=\\"color: #10b981;\\">Available</span></div>";
            }
            hierarchyHtml += "</div>";
        } else {
            hierarchyHtml += "<div style=\\"font-size: 14px; font-weight: 500; margin-bottom: 8px;\\">Root Subnet:</div>";
            hierarchyHtml += "<div style=\\"margin-left: 16px;\\">";
            hierarchyHtml += "<div><i class=\\"fas fa-sitemap\\" style=\\"margin-right: 8px;\\"></i>" + subnet.subnet + " <span style=\\"color: #10b981;\\">" + subnet.status + "</span></div>";
            hierarchyHtml += "</div>";
        }

        // Build action buttons based on status
        var actionButtons = "";

        if (status.can_divide) {
            actionButtons += "<button onclick=\\"showDivideSubnetModal(" + subnet.id + ")\\" style=\\"padding: 10px 20px; border: none; background: #3b82f6; color: white; border-radius: 6px; cursor: pointer; margin-right: 8px;\\"><i class=\\"fas fa-cut\\" style=\\"margin-right: 6px;\\"></i>Divide Subnet</button>";
        }

        if (status.can_generate_ips) {
            actionButtons += "<button onclick=\\"generateSubnetIPs(" + subnet.id + ")\\" style=\\"padding: 10px 20px; border: none; background: #10b981; color: white; border-radius: 6px; cursor: pointer; margin-right: 8px;\\"><i class=\\"fas fa-magic\\" style=\\"margin-right: 6px;\\"></i>Generate IPs</button>";
        }

        if (status.can_regenerate) {
            actionButtons += "<button onclick=\\"regenerateChildSubnets(" + subnet.id + ")\\" style=\\"padding: 10px 20px; border: none; background: #f59e0b; color: white; border-radius: 6px; cursor: pointer; margin-right: 8px;\\"><i class=\\"fas fa-sync\\" style=\\"margin-right: 6px;\\"></i>Regenerate Missing</button>";
        }

        if (status.can_delete) {
            actionButtons += "<button onclick=\\"deleteSubnetSafe(" + subnet.id + ")\\" style=\\"padding: 10px 20px; border: none; background: #dc2626; color: white; border-radius: 6px; cursor: pointer;\\"><i class=\\"fas fa-trash\\" style=\\"margin-right: 6px;\\"></i>Delete Subnet</button>";
        } else {
            // Show disabled delete button with tooltip
            var deleteReasons = [];
            if (status.has_allocated_ips) deleteReasons.push("has allocated IPs");
            if (status.has_children) deleteReasons.push("has child subnets");
            if (status.has_generated_ips && !status.has_allocated_ips) deleteReasons.push("has generated IPs");

            actionButtons += "<button disabled title=\\"Cannot delete: " + deleteReasons.join(", ") + "\\" style=\\"padding: 10px 20px; border: none; background: #d1d5db; color: #6b7280; border-radius: 6px; cursor: not-allowed;\\"><i class=\\"fas fa-trash\\" style=\\"margin-right: 6px;\\"></i>Delete Subnet</button>";
        }

        var statusBg = subnet.status === "Available" ? "#dcfce7" : "#fef3c7";
        var statusColor = subnet.status === "Available" ? "#166534" : "#92400e";
        var statusIcon = subnet.status === "Available" ? "check-circle" : "exclamation-circle";

        // Build the modal content using DOM manipulation instead of innerHTML
        var modalContainer = document.createElement("div");
        modalContainer.style.cssText = "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; width: 90%; max-width: 900px; max-height: 90vh; overflow-y: auto;";

        // Header
        var header = document.createElement("div");
        header.style.cssText = "padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;";
        header.innerHTML = "<div style=\\"display: flex; justify-content: space-between; align-items: center;\\"><h2 style=\\"margin: 0; font-size: 18px; font-weight: 600; color: #111827;\\">Subnet Details</h2><button onclick=\\"closeSubnetDetailsModal()\\" style=\\"background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;\\">&times;</button></div>";

        // Content
        var content = document.createElement("div");
        content.style.cssText = "padding: 0 24px 24px 24px;";

        // Title and status
        var titleSection = document.createElement("div");
        titleSection.style.cssText = "display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;";
        titleSection.innerHTML = "<h1 style=\\"margin: 0; font-size: 24px; font-weight: 600;\\">" + subnet.subnet + "</h1><span style=\\"background: " + statusBg + "; color: " + statusColor + "; padding: 6px 12px; border-radius: 6px; font-size: 14px; font-weight: 500;\\"><i class=\\"fas fa-" + statusIcon + "\\" style=\\"margin-right: 4px;\\"></i>" + subnet.status + "</span>";

        // Subnet information
        var infoSection = document.createElement("div");
        infoSection.style.cssText = "margin-bottom: 32px;";
        var locationText = subnet.location_name || (subnet.city ? subnet.city + (subnet.country ? ", " + subnet.country : "") : "Unassigned");
        var gatewayText = subnet.gateway || subnet.network + ".1";
        infoSection.innerHTML = "<h3 style=\\"margin: 0 0 16px 0; font-size: 16px; font-weight: 600;\\">Subnet Information</h3><div style=\\"display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;\\"><div><div style=\\"font-size: 12px; color: #6b7280; margin-bottom: 4px;\\">Location</div><div style=\\"font-weight: 500;\\">" + locationText + "</div></div><div><div style=\\"font-size: 12px; color: #6b7280; margin-bottom: 4px;\\">Gateway</div><div style=\\"font-weight: 500;\\">" + gatewayText + "</div></div><div><div style=\\"font-size: 12px; color: #6b7280; margin-bottom: 4px;\\">Category</div><div style=\\"font-weight: 500;\\">" + subnet.subnet_type + "</div></div><div><div style=\\"font-size: 12px; color: #6b7280; margin-bottom: 4px;\\">IP Addresses</div><div style=\\"font-weight: 500;\\">" + ipStats.assigned + "/" + ipStats.total + " assigned</div></div><div><div style=\\"font-size: 12px; color: #6b7280; margin-bottom: 4px;\\">Last Updated</div><div style=\\"font-weight: 500;\\">" + new Date(subnet.updated_at).toLocaleDateString() + "</div></div></div>";

        // Note section
        var noteSection = document.createElement("div");
        noteSection.style.cssText = "margin-bottom: 32px;";
        var noteText = subnet.note || "No note added yet.";
        noteSection.innerHTML = "<div style=\\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;\\"><h3 style=\\"margin: 0; font-size: 16px; font-weight: 600;\\">Note</h3><button style=\\"background: none; border: none; color: #6b7280; cursor: pointer;\\"><i class=\\"fas fa-edit\\"></i></button></div><div style=\\"color: #6b7280;\\">" + noteText + "</div>";

        // Hierarchy section
        var hierarchySection = document.createElement("div");
        hierarchySection.style.cssText = "margin-bottom: 32px;";
        hierarchySection.innerHTML = "<div style=\\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;\\"><h3 style=\\"margin: 0; font-size: 16px; font-weight: 600;\\"><i class=\\"fas fa-sitemap\\" style=\\"margin-right: 8px;\\"></i>Subnet Hierarchy</h3></div><div style=\\"background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px;\\">" + hierarchyHtml + "</div>";

        // Actions section
        var actionsSection = document.createElement("div");
        actionsSection.style.cssText = "display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb; flex-wrap: wrap;";
        actionsSection.innerHTML = actionButtons;

        // Assemble the modal
        content.appendChild(titleSection);
        content.appendChild(infoSection);
        content.appendChild(noteSection);
        content.appendChild(hierarchySection);
        content.appendChild(actionsSection);

        modalContainer.appendChild(header);
        modalContainer.appendChild(content);

        modal.appendChild(modalContainer);
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener("click", function(e) {
            if (e.target === modal) {
                closeSubnetDetailsModal();
            }
        });
    }

    function closeSubnetDetailsModal() {
        var modal = document.getElementById("subnetDetailsModal");
        if (modal) {
            modal.remove();
        }
    }

    function showDivideSubnetModal(subnetId) {
        var modal = document.createElement("div");
        modal.id = "divideSubnetModal";
        modal.style.cssText = "display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1001;";

        var modalContent =
            \'<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; width: 90%; max-width: 500px;">\' +
                \'<div style="padding: 24px 24px 0 24px; border-bottom: 1px solid #e5e7eb; margin-bottom: 24px;">\' +
                    \'<div style="display: flex; justify-content: space-between; align-items: center;">\' +
                        \'<h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Divide Subnet</h2>\' +
                        \'<button onclick="closeDivideSubnetModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280;">&times;</button>\' +
                    \'</div>\' +
                \'</div>\' +

                \'<div style="padding: 0 24px 24px 24px;">\' +
                    \'<div style="margin-bottom: 20px;">\' +
                        \'<label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">New Prefix Length</label>\' +
                        \'<input type="number" id="newPrefixLength" min="1" max="30" placeholder="e.g., 26" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px;">\' +
                        \'<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">Enter a larger prefix length to create smaller subnets</div>\' +
                    \'</div>\' +

                    \'<div style="display: flex; justify-content: flex-end; gap: 12px; padding-top: 16px; border-top: 1px solid #e5e7eb;">\' +
                        \'<button onclick="closeDivideSubnetModal()" style="padding: 10px 20px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">Cancel</button>\' +
                        \'<button onclick="performSubnetDivision(\' + subnetId + \')" style="padding: 10px 20px; border: none; background: #3b82f6; color: white; border-radius: 6px; cursor: pointer;">Divide Subnet</button>\' +
                    '</div>' +
                '</div>' +
            '</div>';

        modal.innerHTML = modalContent;
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener("click", function(e) {
            if (e.target === modal) {
                closeDivideSubnetModal();
            }
        });
    }

    function closeDivideSubnetModal() {
        var modal = document.getElementById("divideSubnetModal");
        if (modal) {
            modal.remove();
        }
    }

    function performSubnetDivision(subnetId) {
        var newPrefix = document.getElementById("newPrefixLength").value;
        if (!newPrefix || isNaN(newPrefix)) {
            alert("Please enter a valid prefix length");
            return;
        }

        var formData = new FormData();
        formData.append("subnet_id", subnetId);
        formData.append("new_prefix", newPrefix);

        fetch("' . $modulelink . '&action=ipam&subaction=divide_subnet", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully created ${data.num_created} child subnets!`);
                closeDivideSubnetModal();
                closeSubnetDetailsModal();
                location.reload(); // Refresh to show updated data
            } else {
                alert("Error dividing subnet: " + data.error);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("Error dividing subnet");
        });
    }

    function generateSubnetIPs(subnetId) {
        if (!confirm("Generate IP addresses for this subnet? This will create individual IP entries.")) {
            return;
        }

        var formData = new FormData();
        formData.append("subnet_id", subnetId);

        fetch("' . $modulelink . '&action=ipam&subaction=generate_ips", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully generated ${data.generated_count} IP addresses!`);
                closeSubnetDetailsModal();
                location.reload(); // Refresh to show updated data
            } else {
                alert("Error generating IPs: " + data.error);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("Error generating IP addresses");
        });
    }

    function regenerateChildSubnets(subnetId) {
        if (!confirm("Regenerate missing child subnets? This will create any missing subnets based on existing children.")) {
            return;
        }

        var formData = new FormData();
        formData.append("subnet_id", subnetId);

        fetch("' . $modulelink . '&action=ipam&subaction=regenerate_subnets", {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.regenerated_count > 0) {
                    alert(`Successfully regenerated ${data.regenerated_count} missing child subnets!`);
                    closeSubnetDetailsModal();
                    location.reload(); // Refresh to show updated data
                } else {
                    alert("No missing child subnets found to regenerate.");
                }
            } else {
                alert("Error regenerating subnets: " + data.error);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("Error regenerating child subnets");
        });
    }

    function deleteSubnetSafe(subnetId) {
        if (!confirm("Are you sure you want to delete this subnet? This action cannot be undone.")) {
            return;
        }

        // Use the existing delete function but with safe validation
        deleteSubnet(subnetId);
    }

    function deleteSubnet(subnetId) {
        if (confirm("Are you sure you want to delete this subnet? This will also delete all associated IP addresses.")) {
            var form = document.createElement("form");
            form.method = "post";
            form.style.display = "none";

            var actionInput = document.createElement("input");
            actionInput.type = "hidden";
            actionInput.name = "action";
            actionInput.value = "ipam";
            form.appendChild(actionInput);

            var subactionInput = document.createElement("input");
            subactionInput.type = "hidden";
            subactionInput.name = "subaction";
            subactionInput.value = "subnets";
            form.appendChild(subactionInput);

            var subnetActionInput = document.createElement("input");
            subnetActionInput.type = "hidden";
            subnetActionInput.name = "subnet_action";
            subnetActionInput.value = "delete_subnet";
            form.appendChild(subnetActionInput);

            var subnetIdInput = document.createElement("input");
            subnetIdInput.type = "hidden";
            subnetIdInput.name = "subnet_id";
            subnetIdInput.value = subnetId;
            form.appendChild(subnetIdInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    // Close modal when clicking outside
    document.getElementById("addSubnetModal").addEventListener("click", function(e) {
        if (e.target === this) {
            closeAddSubnetModal();
        }
    });
    </script>';
}

/**
 * Placeholder functions for other IPAM features
 */
function dcim_manage_ips($modulelink) {
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    dcim_generate_sidebar($modulelink);
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IP Address Management</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Address Management</h2>';
    echo '<p>This feature is coming soon.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    dcim_generate_sidebar_javascript($modulelink);
}

function dcim_ip_allocation($modulelink) {
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    dcim_generate_sidebar($modulelink);
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">IP Allocation</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    echo '<h2>IP Allocation</h2>';
    echo '<p>This feature is coming soon.</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Force create IPAM tables if they don't exist
 */
function dcim_force_create_ipam_tables() {
    try {
        // First, check if tables exist but have wrong schema and drop them
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            // Check if the subnet column exists (new schema)
            if (!Capsule::schema()->hasColumn('dcim_subnets', 'subnet')) {
                // Old schema detected, drop and recreate
                Capsule::statement('SET FOREIGN_KEY_CHECKS=0');
                Capsule::schema()->dropIfExists('dcim_ip_assignments');
                Capsule::schema()->dropIfExists('dcim_ip_addresses');
                Capsule::schema()->dropIfExists('dcim_subnets');
                Capsule::statement('SET FOREIGN_KEY_CHECKS=1');
                echo '<div class="alert alert-info">Dropped old IPAM tables with incorrect schema.</div>';
            } else {
                // Check if parent_id column exists for hierarchy support
                if (!Capsule::schema()->hasColumn('dcim_subnets', 'parent_id')) {
                    Capsule::schema()->table('dcim_subnets', function ($table) {
                        $table->integer('parent_id')->unsigned()->nullable()->after('id');
                        $table->foreign('parent_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
                        $table->index(['parent_id']);
                    });
                    echo '<div class="alert alert-success">Added parent_id column to dcim_subnets for hierarchy support.</div>';
                }
            }
        }

        // Create locations table if it doesn't exist (needed for foreign key)
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->text('address')->nullable();
                $table->string('city')->nullable();
                $table->string('country')->nullable();
                $table->string('contact_name')->nullable();
                $table->string('contact_email')->nullable();
                $table->string('contact_phone')->nullable();
                $table->integer('total_power_capacity')->default(0);
                $table->string('power_unit')->default('Watts');
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
            });
            echo '<div class="alert alert-success">Created dcim_locations table successfully.</div>';
        }

        // Check and create IPAM subnets table with correct schema
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::schema()->create('dcim_subnets', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
                $table->string('network'); // Network address, e.g., ***********
                $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
                $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
                $table->string('country')->nullable();
                $table->string('city')->nullable();
                $table->boolean('is_public')->default(false);
                $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
                $table->string('gateway')->nullable();
                $table->string('dns_primary')->nullable();
                $table->string('dns_secondary')->nullable();
                $table->string('vlan_id')->nullable();
                $table->text('note')->nullable();
                $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
                $table->timestamps();

                // Add foreign key to locations if it exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
                }

                // Add unique constraint on subnet CIDR
                $table->unique(['subnet']);
            });

            echo '<div class="alert alert-success">Created dcim_subnets table successfully.</div>';
        }

        // Check and create IPAM IP addresses table
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address');
                $table->string('hostname')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
                $table->timestamps();

                // Add foreign key to subnets
                $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');

                // Ensure each IP is unique within a subnet
                $table->unique(['subnet_id', 'ip_address']);
            });

            echo '<div class="alert alert-success">Created dcim_ip_addresses table successfully.</div>';
        }

        // Check and create IPAM IP assignments table (links IPs to devices)
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                $table->increments('id');
                $table->integer('ip_address_id')->unsigned();
                $table->enum('device_type', ['server', 'switch', 'chassis']);
                $table->integer('device_id')->unsigned();
                $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
                $table->timestamps();

                // Add foreign key to IP addresses
                $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');

                // Ensure one IP can't be assigned multiple times to the same interface
                $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
            });

            echo '<div class="alert alert-success">Created dcim_ip_assignments table successfully.</div>';
        }

        return true;
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error creating IPAM tables: ' . htmlspecialchars($e->getMessage()) . '</div>';
        error_log("DCIM: Error creating IPAM tables - " . $e->getMessage());
        return false;
    }
}

/**
 * Check if a subnet has any IP addresses allocated to devices
 */
function dcim_subnet_has_allocated_ips($subnet_id) {
    try {
        $allocated_count = Capsule::table('dcim_ip_addresses')
            ->join('dcim_ip_assignments', 'dcim_ip_addresses.id', '=', 'dcim_ip_assignments.ip_address_id')
            ->where('dcim_ip_addresses.subnet_id', $subnet_id)
            ->count();

        return $allocated_count > 0;
    } catch (Exception $e) {
        error_log("DCIM: Error checking subnet allocation - " . $e->getMessage());
        return true; // Err on the side of caution
    }
}

/**
 * Check if a subnet has any child subnets
 */
function dcim_subnet_has_children($subnet_id) {
    try {
        $children_count = Capsule::table('dcim_subnets')
            ->where('parent_id', $subnet_id)
            ->count();

        return $children_count > 0;
    } catch (Exception $e) {
        error_log("DCIM: Error checking subnet children - " . $e->getMessage());
        return true; // Err on the side of caution
    }
}

/**
 * Check if a subnet has any generated IP addresses
 */
function dcim_subnet_has_generated_ips($subnet_id) {
    try {
        $ip_count = Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $subnet_id)
            ->count();

        return $ip_count > 0;
    } catch (Exception $e) {
        error_log("DCIM: Error checking generated IPs - " . $e->getMessage());
        return true; // Err on the side of caution
    }
}

/**
 * Get subnet allocation status and available actions
 */
function dcim_get_subnet_status($subnet_id) {
    $has_allocated_ips = dcim_subnet_has_allocated_ips($subnet_id);
    $has_children = dcim_subnet_has_children($subnet_id);
    $has_generated_ips = dcim_subnet_has_generated_ips($subnet_id);

    return [
        'has_allocated_ips' => $has_allocated_ips,
        'has_children' => $has_children,
        'has_generated_ips' => $has_generated_ips,
        'can_divide' => !$has_allocated_ips && !$has_children,
        'can_generate_ips' => !$has_allocated_ips && !$has_children,
        'can_delete' => !$has_allocated_ips && !$has_children && !$has_generated_ips,
        'can_regenerate' => $has_children
    ];
}

/**
 * Divide a subnet into smaller subnets with allocation checking
 */
function dcim_divide_subnet($subnet_id, $new_prefix_length) {
    try {
        // Get parent subnet
        $parent_subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$parent_subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }

        // Check if subnet can be divided
        $status = dcim_get_subnet_status($subnet_id);
        if (!$status['can_divide']) {
            $reasons = [];
            if ($status['has_allocated_ips']) $reasons[] = 'has allocated IP addresses';
            if ($status['has_children']) $reasons[] = 'already has child subnets';
            return ['success' => false, 'error' => 'Cannot divide subnet: ' . implode(' and ', $reasons)];
        }

        // Validate new prefix length
        if ($new_prefix_length <= $parent_subnet->prefix_length || $new_prefix_length > 30) {
            return ['success' => false, 'error' => 'Invalid new prefix length. Must be larger than current (' . $parent_subnet->prefix_length . ') and not exceed 30'];
        }

        // Calculate subnet division parameters
        $prefix_diff = $new_prefix_length - $parent_subnet->prefix_length;
        $num_subnets = pow(2, $prefix_diff);
        $subnet_size = pow(2, 32 - $new_prefix_length);

        $parent_network_long = ip2long($parent_subnet->network);
        $created_subnets = [];

        // Create child subnets
        for ($i = 0; $i < $num_subnets; $i++) {
            $subnet_network_long = $parent_network_long + ($i * $subnet_size);
            $subnet_network = long2ip($subnet_network_long);
            $subnet_cidr = $subnet_network . '/' . $new_prefix_length;

            $new_subnet_id = Capsule::table('dcim_subnets')->insertGetId([
                'parent_id' => $subnet_id,
                'subnet' => $subnet_cidr,
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length,
                'ip_version' => $parent_subnet->ip_version,
                'location_id' => $parent_subnet->location_id,
                'country' => $parent_subnet->country,
                'city' => $parent_subnet->city,
                'is_public' => $parent_subnet->is_public,
                'subnet_type' => $parent_subnet->subnet_type,
                'note' => "Child subnet created from {$parent_subnet->subnet}",
                'status' => 'Available',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $created_subnets[] = [
                'id' => $new_subnet_id,
                'subnet' => $subnet_cidr,
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length
            ];
        }

        // Update parent subnet status
        Capsule::table('dcim_subnets')
            ->where('id', $subnet_id)
            ->update([
                'status' => 'Parent of Allocated Subnet',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        // Remove any generated IPs from parent subnet since it's now divided
        Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet_id)->delete();

        return ['success' => true, 'created_subnets' => $created_subnets, 'num_created' => count($created_subnets)];

    } catch (Exception $e) {
        error_log("DCIM: Error dividing subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Generate IP addresses for a subnet with allocation awareness
 */
function dcim_generate_subnet_ips_safe($subnet_id) {
    try {
        // Get subnet details
        $subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }

        // Check if subnet can have IPs generated
        $status = dcim_get_subnet_status($subnet_id);
        if (!$status['can_generate_ips']) {
            $reasons = [];
            if ($status['has_allocated_ips']) $reasons[] = 'has allocated IP addresses';
            if ($status['has_children']) $reasons[] = 'has child subnets';
            return ['success' => false, 'error' => 'Cannot generate IPs: ' . implode(' and ', $reasons)];
        }

        // Check if IPs already exist
        if ($status['has_generated_ips']) {
            return ['success' => false, 'error' => 'IP addresses already generated for this subnet'];
        }

        // Generate IPs only for IPv4 subnets
        if ($subnet->ip_version !== 'IPv4') {
            return ['success' => false, 'error' => 'IP generation is only supported for IPv4 subnets'];
        }

        // Calculate IP range
        $network_long = ip2long($subnet->network);
        $total_ips = pow(2, 32 - $subnet->prefix_length);

        // Limit generation for large subnets (performance)
        $max_ips = min($total_ips, 1000);

        $ips = [];
        for ($i = 0; $i < $max_ips; $i++) {
            $ip_long = $network_long + $i;
            $ip_address = long2ip($ip_long);
            $status = 'available';

            // Mark special addresses as reserved
            if ($i === 0) {
                $status = 'reserved'; // Network address
            } elseif ($i === $total_ips - 1 && $subnet->prefix_length < 31) {
                $status = 'reserved'; // Broadcast address
            } elseif ($ip_address === $subnet->gateway) {
                $status = 'reserved'; // Gateway address
            }

            $ips[] = [
                'subnet_id' => $subnet_id,
                'ip_address' => $ip_address,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        // Batch insert for performance
        if (!empty($ips)) {
            Capsule::table('dcim_ip_addresses')->insert($ips);
        }

        return ['success' => true, 'generated_count' => count($ips), 'total_possible' => $total_ips];

    } catch (Exception $e) {
        error_log("DCIM: Error generating subnet IPs - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Smart subnet deletion with allocation checking
 */
function dcim_delete_subnet_safe($subnet_id) {
    try {
        // Get subnet details
        $subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }

        // Check if subnet can be deleted
        $status = dcim_get_subnet_status($subnet_id);
        if (!$status['can_delete']) {
            $reasons = [];
            if ($status['has_allocated_ips']) $reasons[] = 'has IP addresses allocated to devices';
            if ($status['has_children']) $reasons[] = 'has child subnets';
            if ($status['has_generated_ips'] && !$status['has_allocated_ips']) $reasons[] = 'has generated IP addresses (delete IPs first)';
            return ['success' => false, 'error' => 'Cannot delete subnet: ' . implode(' and ', $reasons)];
        }

        // Delete all IP addresses first (should be none if validation passed)
        $deleted_ips = Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet_id)->delete();

        // Delete the subnet
        $deleted = Capsule::table('dcim_subnets')->where('id', $subnet_id)->delete();

        // If this was a child subnet, check if parent should be updated
        if ($subnet->parent_id) {
            $remaining_children = Capsule::table('dcim_subnets')
                ->where('parent_id', $subnet->parent_id)
                ->count();

            // If no children remain, update parent status back to Available
            if ($remaining_children === 0) {
                Capsule::table('dcim_subnets')
                    ->where('id', $subnet->parent_id)
                    ->update([
                        'status' => 'Available',
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
        }

        return ['success' => true, 'deleted' => $deleted > 0, 'deleted_ips' => $deleted_ips];

    } catch (Exception $e) {
        error_log("DCIM: Error deleting subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Regenerate missing child subnets for a parent subnet
 */
function dcim_regenerate_child_subnets($subnet_id) {
    try {
        // Get parent subnet
        $parent_subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$parent_subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }

        // Get existing child subnets
        $existing_children = Capsule::table('dcim_subnets')
            ->where('parent_id', $subnet_id)
            ->orderBy('network')
            ->get();

        if (empty($existing_children)) {
            return ['success' => false, 'error' => 'No child subnets found to regenerate from'];
        }

        // Determine the child subnet prefix length from existing children
        $child_prefix = $existing_children[0]->prefix_length;

        // Calculate expected number of child subnets
        $prefix_diff = $child_prefix - $parent_subnet->prefix_length;
        $expected_count = pow(2, $prefix_diff);
        $subnet_size = pow(2, 32 - $child_prefix);

        $parent_network_long = ip2long($parent_subnet->network);
        $created_subnets = [];
        $existing_networks = [];

        // Build list of existing child networks
        foreach ($existing_children as $child) {
            $existing_networks[] = $child->network;
        }

        // Generate all expected child subnets and create missing ones
        for ($i = 0; $i < $expected_count; $i++) {
            $subnet_network_long = $parent_network_long + ($i * $subnet_size);
            $subnet_network = long2ip($subnet_network_long);

            // Skip if this subnet already exists
            if (in_array($subnet_network, $existing_networks)) {
                continue;
            }

            $subnet_cidr = $subnet_network . '/' . $child_prefix;

            $new_subnet_id = Capsule::table('dcim_subnets')->insertGetId([
                'parent_id' => $subnet_id,
                'subnet' => $subnet_cidr,
                'network' => $subnet_network,
                'prefix_length' => $child_prefix,
                'ip_version' => $parent_subnet->ip_version,
                'location_id' => $parent_subnet->location_id,
                'country' => $parent_subnet->country,
                'city' => $parent_subnet->city,
                'is_public' => $parent_subnet->is_public,
                'subnet_type' => $parent_subnet->subnet_type,
                'note' => "Regenerated child subnet from {$parent_subnet->subnet}",
                'status' => 'Available',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $created_subnets[] = [
                'id' => $new_subnet_id,
                'subnet' => $subnet_cidr,
                'network' => $subnet_network,
                'prefix_length' => $child_prefix
            ];
        }

        // Update parent subnet status if it wasn't already
        if ($parent_subnet->status !== 'Parent of Allocated Subnet') {
            Capsule::table('dcim_subnets')
                ->where('id', $subnet_id)
                ->update([
                    'status' => 'Parent of Allocated Subnet',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }

        return [
            'success' => true,
            'regenerated_count' => count($created_subnets),
            'expected_total' => $expected_count,
            'existing_count' => count($existing_children),
            'created_subnets' => $created_subnets
        ];

    } catch (Exception $e) {
        error_log("DCIM: Error regenerating child subnets - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}
