<?php
/**
 * DCIM (Data Center Infrastructure Management) Add-on for WHMCS
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Add-on configuration
 */
function dcim_config() {
    return [
        'name' => 'DCIM - Data Center Infrastructure Management',
        'description' => 'Manage data center locations, racks, and servers with visual rack layouts.',
        'version' => '1.0.0',
        'author' => 'Your Name',
        'language' => 'english',
        'fields' => [
            'enable_client_access' => [
                'FriendlyName' => 'Enable Client Access',
                'Type' => 'yesno',
                'Description' => 'Allow clients to view their assigned servers in racks',
                'Default' => 'yes'
            ],
            'default_rack_units' => [
                'FriendlyName' => 'Default Rack Units',
                'Type' => 'text',
                'Description' => 'Default number of U units per rack',
                'Default' => '42'
            ],
            'power_unit' => [
                'FriendlyName' => 'Power Unit',
                'Type' => 'dropdown',
                'Options' => 'Watts,Kilowatts,Amps',
                'Default' => 'Watts',
                'Description' => 'Unit for power measurements'
            ]
        ]
    ];
}

/**
 * Create sample data for demo purposes
 */
function dcim_create_sample_data() {
    try {
        // Ensure tables exist first
        if (!dcim_ensure_tables_exist()) {
            return false;
        }

        // Check if data already exists
        $existing_locations = Capsule::table('dcim_locations')->count();
        if ($existing_locations > 0) {
            return true; // Data already exists
        }

        // Create sample locations
        $location_data = [
            [
                'name' => 'Primary Data Center',
                'address' => '123 Tech Street, Silicon Valley',
                'city' => 'San Jose',
                'country' => 'United States',
                'contact_name' => 'John Smith',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-0123',
                'total_power_capacity' => 50000,
                'power_unit' => 'Watts',
                'notes' => 'Primary data center facility',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'European Data Center',
                'address' => '456 Server Lane, Frankfurt',
                'city' => 'Frankfurt',
                'country' => 'Germany',
                'contact_name' => 'Anna Mueller',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+49-30-12345678',
                'total_power_capacity' => 30000,
                'power_unit' => 'Watts',
                'notes' => 'European operations center',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($location_data as $location) {
            $location_id = Capsule::table('dcim_locations')->insertGetId($location);

            // Create sample racks for each location
            $rack_data = [
                [
                    'location_id' => $location_id,
                    'name' => 'A01',
                    'row' => 'A',
                    'position' => '01',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A01-A',
                    'pdu_b' => 'PDU-A01-B',
                    'notes' => 'Primary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'location_id' => $location_id,
                    'name' => 'A02',
                    'row' => 'A',
                    'position' => '02',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A02-A',
                    'pdu_b' => 'PDU-A02-B',
                    'notes' => 'Secondary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            foreach ($rack_data as $rack) {
                $rack_id = Capsule::table('dcim_racks')->insertGetId($rack);

                // Create sample servers for the first rack only
                if ($rack['name'] == 'A01') {
                    $server_data = [
                        [
                            'rack_id' => $rack_id,
                            'name' => 'WEB-01',
                            'hostname' => 'web01.example.com',
                            'start_unit' => 40,
                            'unit_size' => 1,
                            'make' => 'Dell',
                            'model' => 'PowerEdge R640',
                            'serial_number' => 'DL001234',
                            'specifications' => '2x Intel Xeon Silver 4214, 64GB RAM, 2x 1TB SSD',
                            'power_consumption' => 300,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Web server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        [
                            'rack_id' => $rack_id,
                            'name' => 'DB-01',
                            'hostname' => 'db01.example.com',
                            'start_unit' => 37,
                            'unit_size' => 2,
                            'make' => 'HPE',
                            'model' => 'ProLiant DL380',
                            'serial_number' => 'HP005678',
                            'specifications' => '2x Intel Xeon Gold 6242, 128GB RAM, 4x 2TB SSD RAID 10',
                            'power_consumption' => 450,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Database server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]
                    ];

                    foreach ($server_data as $server) {
                        Capsule::table('dcim_servers')->insert($server);
                    }
                }
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating sample data - " . $e->getMessage());
        return false;
    }
}

/**
 * Handle sample data creation request
 */
function dcim_handle_sample_data($modulelink) {
    if ($_GET['create_sample'] === 'true') {
        if (dcim_create_sample_data()) {
            echo '<div class="alert alert-success">Sample data created successfully! <a href="' . $modulelink . '">Refresh page</a></div>';
        } else {
            echo '<div class="alert alert-danger">Error creating sample data. Check error logs for details.</div>';
        }
        return true;
    }
    return false;
}

/**
 * Generate consistent sidebar for all pages
 */
function dcim_generate_sidebar($modulelink, $selected_location_id = null) {
    // Get all data with error handling
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        $total_servers = Capsule::table('dcim_servers')->count();
        $total_switches = Capsule::table('dcim_switches')->count();
        $total_chassies = Capsule::table('dcim_chassies')->count();
        $total_racks = Capsule::table('dcim_racks')->count();
        $total_subnets = Capsule::table('dcim_subnets')->where('status', 'active')->count();
        $total_ips = Capsule::table('dcim_ip_addresses')->count();
        $assigned_ips = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching sidebar data - " . $e->getMessage());
        $locations = collect([]);
        $total_servers = $total_switches = $total_chassies = $total_racks = 0;
        $total_subnets = $total_ips = $assigned_ips = 0;
    }
    
    echo '<div class="dcim-sidebar">';
    
    // Main Menu Header
    echo '<div class="sidebar-header" style="padding: 16px; border-bottom: 2px solid #e5e7eb; margin-bottom: 16px;">';
    echo '<div class="sidebar-title" style="font-size: 18px; font-weight: 700; color: #1f2937; display: flex; align-items: center;">';
    echo '<i class="fas fa-database" style="margin-right: 10px; color: #4f46e5;"></i>DCIM Dashboard';
    echo '</div>';
    echo '<div class="last-updated" style="font-size: 11px; color: #6b7280; margin-top: 4px;">';
    echo '<span>Last updated: ' . date('H:i') . '</span>';
    echo '<i class="fas fa-sync-alt" style="margin-left: 6px;"></i>';
    echo '</div>';
    echo '</div>';
    
    // IPAM Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'ipam\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-network-wired" style="color: #059669;"></i>';
    echo '<span>IPAM</span>';
    echo '<div class="menu-stats">';
    if ($total_ips > 0) {
        $utilization = round(($assigned_ips / $total_ips) * 100, 1);
        $color = $utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981');
        echo '<span style="background: ' . $color . ';">' . $utilization . '%</span>';
    }
    echo '<span style="background: #6b7280;">' . $total_subnets . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content" id="ipam-menu">';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'dashboard\')">';
    echo '<i class="fas fa-chart-pie"></i>';
    echo '<span>Dashboard</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'subnets\')">';
    echo '<i class="fas fa-sitemap"></i>';
    echo '<span>Subnets</span>';
    echo '<span class="item-count">' . $total_subnets . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'ips\')">';
    echo '<i class="fas fa-list-ol"></i>';
    echo '<span>IP Addresses</span>';
    echo '<span class="item-count">' . $assigned_ips . '/' . $total_ips . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateToIPAM(\'allocation\')">';
    echo '<i class="fas fa-share-alt"></i>';
    echo '<span>IP Allocation</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Inventory Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'inventory\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-server" style="color: #dc2626;"></i>';
    echo '<span>Inventory</span>';
    echo '<div class="menu-stats">';
    $total_devices = $total_servers + $total_switches + $total_chassies;
    echo '<span style="background: #6b7280;">' . $total_devices . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content" id="inventory-menu">';
    echo '<div class="menu-item" onclick="navigateTo(\'servers\')">';
    echo '<i class="fas fa-server"></i>';
    echo '<span>Servers</span>';
    echo '<span class="item-count">' . $total_servers . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'switches\')">';
    echo '<i class="fas fa-network-wired"></i>';
    echo '<span>Switches</span>';
    echo '<span class="item-count">' . $total_switches . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'chassies\')">';
    echo '<i class="fas fa-hdd"></i>';
    echo '<span>Chassis</span>';
    echo '<span class="item-count">' . $total_chassies . '</span>';
    echo '</div>';
    echo '<div class="menu-item" onclick="navigateTo(\'racks\')">';
    echo '<i class="fas fa-th-large"></i>';
    echo '<span>Racks</span>';
    echo '<span class="item-count">' . $total_racks . '</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Locations Section
    echo '<div class="sidebar-section menu-section">';
    echo '<div class="menu-header" onclick="toggleMenuSection(\'locations\')">';
    echo '<div class="menu-title">';
    echo '<i class="fas fa-map-marker-alt" style="color: #7c3aed;"></i>';
    echo '<span>Locations</span>';
    echo '<div class="menu-stats">';
    echo '<span style="background: #6b7280;">' . count($locations) . '</span>';
    echo '</div>';
    echo '</div>';
    echo '<i class="fas fa-chevron-down toggle-icon"></i>';
    echo '</div>';
    
    echo '<div class="menu-content expanded" id="locations-menu">';
    echo '<div class="menu-item" onclick="navigateTo(\'locations\')">';
    echo '<i class="fas fa-cog"></i>';
    echo '<span>Manage Locations</span>';
    echo '</div>';
    
    echo '<div class="search-container" style="margin: 8px 0;">';
    echo '<div class="search-box">';
    echo '<i class="fas fa-search search-icon"></i>';
    echo '<input type="text" class="search-input" placeholder="Search locations..." id="locationSearch">';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="locations-list" style="max-height: 300px; overflow-y: auto;">';
    if (count($locations) > 0) {
        foreach ($locations as $location) {
            $isActive = ($selected_location_id == $location->id) ? 'active' : '';
            $rack_count = 0;
            
            try {
                $rack_count = Capsule::table('dcim_racks')->where('location_id', $location->id)->count();
            } catch (Exception $e) {
                error_log("DCIM: Error counting racks - " . $e->getMessage());
            }
            
            echo '<div class="location-item ' . $isActive . '" onclick="selectLocation(' . $location->id . ')">';
            echo '<div class="location-flag">' . dcim_get_country_flag($location->country) . '</div>';
            echo '<div class="location-info">';
            echo '<div class="location-name">' . htmlspecialchars($location->name) . '</div>';
            if ($location->city || $location->country) {
                $location_details = [];
                if ($location->city) $location_details[] = $location->city;
                if ($location->country) $location_details[] = $location->country;
                echo '<div style="font-size: 11px; color: #9ca3af; margin-top: 2px;">' . htmlspecialchars(implode(', ', $location_details)) . '</div>';
            }
            if ($rack_count > 0) {
                echo '<div style="font-size: 11px; color: #718096; margin-top: 2px;">' . $rack_count . ' rack' . ($rack_count != 1 ? 's' : '') . '</div>';
            }
            echo '</div>';
            echo '<div class="location-actions">';
            echo '<button class="action-btn" onclick="event.stopPropagation(); editLocation(' . $location->id . ')" title="Edit"><i class="fas fa-edit"></i></button>';
            echo '<button class="action-btn" onclick="event.stopPropagation(); deleteLocation(' . $location->id . ')" title="Delete"><i class="fas fa-trash"></i></button>';
            echo '<button class="action-btn" onclick="event.stopPropagation(); addRack(' . $location->id . ')" title="Add Rack"><i class="fas fa-plus"></i></button>';
            echo '</div>';
            echo '</div>';
        }
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-map-marker-alt"></i>';
        echo '<p>No locations found</p>';
        echo '<p style="font-size: 11px; margin-top: 4px;">Add your first data center location</p>';
        echo '</div>';
    }
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
}

/**
 * Generate shared JavaScript for sidebar interactions
 */
function dcim_generate_sidebar_javascript($modulelink) {
    echo '<script>
    function selectLocation(locationId) {
        window.location.href = "' . $modulelink . '&location_id=" + locationId;
    }
    
    function editLocation(locationId) {
        window.location.href = "' . $modulelink . '&action=locations&edit=" + locationId;
    }
    
    function deleteLocation(locationId) {
        if (confirm("Are you sure you want to delete this location and all its racks and servers?")) {
            window.location.href = "' . $modulelink . '&action=locations&delete=" + locationId;
        }
    }
    
    function addRack(locationId) {
        window.location.href = "' . $modulelink . '&action=racks&location_id=" + locationId;
    }
    
    function showAddLocationModal() {
        window.location.href = "' . $modulelink . '&action=locations";
    }
    
    function showAddSubnetModal() {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets";
    }
    
    function navigateToIPAM(subaction) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=" + subaction;
    }
    
    function navigateTo(action) {
        window.location.href = "' . $modulelink . '&action=" + action;
    }
    
    function toggleMenuSection(sectionId) {
        var menuContent = document.getElementById(sectionId + "-menu");
        var menuHeaders = document.querySelectorAll(".menu-header");
        var toggleIcon = null;
        
        for (var i = 0; i < menuHeaders.length; i++) {
            if (menuHeaders[i].getAttribute("onclick") && menuHeaders[i].getAttribute("onclick").includes(sectionId)) {
                toggleIcon = menuHeaders[i].querySelector(".toggle-icon");
                break;
            }
        }
        
        if (menuContent && toggleIcon) {
            if (menuContent.classList.contains("expanded")) {
                menuContent.classList.remove("expanded");
                toggleIcon.style.transform = "rotate(-90deg)";
            } else {
                menuContent.classList.add("expanded");
                toggleIcon.style.transform = "rotate(0deg)";
            }
        }
    }
    
    // Initialize menu states when DOM is ready
    document.addEventListener("DOMContentLoaded", function() {
        // IPAM and Inventory sections start collapsed, Locations starts expanded
        setTimeout(function() {
            var ipamMenu = document.getElementById("ipam-menu");
            var inventoryMenu = document.getElementById("inventory-menu");
            var locationsMenu = document.getElementById("locations-menu");
            
            // Find toggle icons by their parent menu headers
            var menuHeaders = document.querySelectorAll(".menu-header");
            var ipamIcon = null;
            var inventoryIcon = null;
            var locationsIcon = null;
            
            for (var i = 0; i < menuHeaders.length; i++) {
                var onclick = menuHeaders[i].getAttribute("onclick");
                if (onclick) {
                    if (onclick.includes("ipam")) {
                        ipamIcon = menuHeaders[i].querySelector(".toggle-icon");
                    } else if (onclick.includes("inventory")) {
                        inventoryIcon = menuHeaders[i].querySelector(".toggle-icon");
                    } else if (onclick.includes("locations")) {
                        locationsIcon = menuHeaders[i].querySelector(".toggle-icon");
                    }
                }
            }
            
            if (ipamMenu) {
                ipamMenu.classList.remove("expanded");
                if (ipamIcon) ipamIcon.style.transform = "rotate(-90deg)";
            }
            
            if (inventoryMenu) {
                inventoryMenu.classList.remove("expanded");
                if (inventoryIcon) inventoryIcon.style.transform = "rotate(-90deg)";
            }
            
            if (locationsMenu) {
                locationsMenu.classList.add("expanded");
                if (locationsIcon) locationsIcon.style.transform = "rotate(0deg)";
            }
        }, 100);
    });
    
    // Search functionality
    document.addEventListener("DOMContentLoaded", function() {
        var searchInput = document.getElementById("locationSearch");
        if (searchInput) {
            searchInput.addEventListener("input", function() {
                const searchTerm = this.value.toLowerCase();
                const items = document.querySelectorAll(".location-item");
                
                items.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        item.style.display = "";
                    } else {
                        item.style.display = "none";
                    }
                });
            });
        }
    });
    
    </script>';
}

/**
 * Get country flag emoji based on country name
 */
function dcim_get_country_flag($country) {
    if (empty($country)) {
        return '🌍'; // Default globe icon
    }
    
    $country = strtolower(trim($country));
    
    $country_flags = [
        'united states' => '🇺🇸',
        'usa' => '🇺🇸',
        'us' => '🇺🇸',
        'germany' => '🇩🇪',
        'united kingdom' => '🇬🇧',
        'uk' => '🇬🇧',
        'france' => '🇫🇷',
        'canada' => '🇨🇦',
        'australia' => '🇦🇺',
        'japan' => '🇯🇵',
        'china' => '🇨🇳',
        'india' => '🇮🇳',
        'brazil' => '🇧🇷',
        'russia' => '🇷🇺',
        'south korea' => '🇰🇷',
        'italy' => '🇮🇹',
        'spain' => '🇪🇸',
        'netherlands' => '🇳🇱',
        'sweden' => '🇸🇪',
        'norway' => '🇳🇴',
        'denmark' => '🇩🇰',
        'finland' => '🇫🇮',
        'switzerland' => '🇨🇭',
        'austria' => '🇦🇹',
        'belgium' => '🇧🇪',
        'ireland' => '🇮🇪',
        'portugal' => '🇵🇹',
        'poland' => '🇵🇱',
        'czech republic' => '🇨🇿',
        'hungary' => '🇭🇺',
        'greece' => '🇬🇷',
        'turkey' => '🇹🇷',
        'israel' => '🇮🇱',
        'south africa' => '🇿🇦',
        'mexico' => '🇲🇽',
        'argentina' => '🇦🇷',
        'chile' => '🇨🇱',
        'colombia' => '🇨🇴',
        'peru' => '🇵🇪',
        'venezuela' => '🇻🇪',
        'thailand' => '🇹🇭',
        'vietnam' => '🇻🇳',
        'singapore' => '🇸🇬',
        'malaysia' => '🇲🇾',
        'indonesia' => '🇮🇩',
        'philippines' => '🇵🇭',
        'new zealand' => '🇳🇿',
        'ukraine' => '🇺🇦',
        'romania' => '🇷🇴',
        'bulgaria' => '🇧🇬',
        'croatia' => '🇭🇷',
        'slovenia' => '🇸🇮',
        'slovakia' => '🇸🇰',
        'lithuania' => '🇱🇹',
        'latvia' => '🇱🇻',
        'estonia' => '🇪🇪',
        'serbia' => '🇷🇸',
        'bosnia and herzegovina' => '🇧🇦',
        'montenegro' => '🇲🇪',
        'north macedonia' => '🇲🇰',
        'albania' => '🇦🇱',
        'luxembourg' => '🇱🇺',
        'iceland' => '🇮🇸',
        'malta' => '🇲🇹',
        'cyprus' => '🇨🇾',
        'monaco' => '🇲🇨',
        'liechtenstein' => '🇱🇮',
        'andorra' => '🇦🇩',
        'san marino' => '🇸🇲',
        'vatican city' => '🇻🇦',
        'egypt' => '🇪🇬',
        'morocco' => '🇲🇦',
        'tunisia' => '🇹🇳',
        'algeria' => '🇩🇿',
        'libya' => '🇱🇾',
        'nigeria' => '🇳🇬',
        'kenya' => '🇰🇪',
        'ghana' => '🇬🇭',
        'ethiopia' => '🇪🇹',
        'uganda' => '🇺🇬',
        'tanzania' => '🇹🇿',
        'cameroon' => '🇨🇲',
        'ivory coast' => '🇨🇮',
        'senegal' => '🇸🇳',
        'madagascar' => '🇲🇬',
        'botswana' => '🇧🇼',
        'namibia' => '🇳🇦',
        'zambia' => '🇿🇲',
        'zimbabwe' => '🇿🇼',
        'mozambique' => '🇲🇿',
        'malawi' => '🇲🇼',
        'rwanda' => '🇷🇼',
        'burundi' => '🇧🇮',
        'djibouti' => '🇩🇯',
        'eritrea' => '🇪🇷',
        'somalia' => '🇸🇴',
        'sudan' => '🇸🇩',
        'south sudan' => '🇸🇸',
        'chad' => '🇹🇩',
        'central african republic' => '🇨🇫',
        'democratic republic of the congo' => '🇨🇩',
        'republic of the congo' => '🇨🇬',
        'gabon' => '🇬🇦',
        'equatorial guinea' => '🇬🇶',
        'sao tome and principe' => '🇸🇹',
        'cape verde' => '🇨🇻',
        'guinea' => '🇬🇳',
        'guinea-bissau' => '🇬🇼',
        'sierra leone' => '🇸🇱',
        'liberia' => '🇱🇷',
        'burkina faso' => '🇧🇫',
        'mali' => '🇲🇱',
        'niger' => '🇳🇪',
        'mauritania' => '🇲🇷',
        'the gambia' => '🇬🇲',
        'gambia' => '🇬🇲',
        'mauritius' => '🇲🇺',
        'seychelles' => '🇸🇨',
        'comoros' => '🇰🇲',
        'lesotho' => '🇱🇸',
        'eswatini' => '🇸🇿',
        'swaziland' => '🇸🇿'
    ];
    
    return $country_flags[$country] ?? '🌍';
}



function dcim_ensure_tables_exist() {
    try {
        // Check and create locations table
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->text('address')->nullable();
                $table->string('city')->nullable();
                $table->string('country')->nullable();
                $table->string('contact_name')->nullable();
                $table->string('contact_email')->nullable();
                $table->string('contact_phone')->nullable();
                $table->integer('total_power_capacity')->default(0);
                $table->string('power_unit')->default('Watts');
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
            });
        }

        // Check and create racks table
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::schema()->create('dcim_racks', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('row')->nullable();
                $table->string('position')->nullable();
                $table->integer('units')->default(42);
                $table->integer('power_capacity')->default(0);
                $table->string('pdu_a')->nullable();
                $table->string('pdu_b')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'maintenance', 'inactive'])->default('active');
                $table->timestamps();
                
                // Add foreign key only if locations table exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
                }
            });
        }

        // Check and create CPU models table
        if (!Capsule::schema()->hasTable('dcim_cpu_models')) {
            Capsule::schema()->create('dcim_cpu_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('cores')->nullable();
                $table->string('frequency')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create RAM configurations table
        if (!Capsule::schema()->hasTable('dcim_ram_configs')) {
            Capsule::schema()->create('dcim_ram_configs', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('size')->nullable();
                $table->string('type')->nullable();
                $table->string('speed')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create switch models table
        if (!Capsule::schema()->hasTable('dcim_switch_models')) {
            Capsule::schema()->create('dcim_switch_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('model_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('port_speed')->nullable(); // 1G, 10G, etc.
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create chassis models table
        if (!Capsule::schema()->hasTable('dcim_chassis_models')) {
            Capsule::schema()->create('dcim_chassis_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create servers table
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::schema()->create('dcim_servers', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->text('specifications')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->string('ip_address')->nullable();
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create switches table
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::schema()->create('dcim_switches', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create chassies table
        if (!Capsule::schema()->hasTable('dcim_chassies')) {
            Capsule::schema()->create('dcim_chassies', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(10); // Chassis are typically larger
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('slots')->default(8); // Blade slots
                $table->string('chassis_type')->nullable(); // blade, storage, network
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create IPAM subnets table
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::schema()->create('dcim_subnets', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('network'); // e.g., ***********
                $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
                $table->string('gateway')->nullable();
                $table->string('dns_primary')->nullable();
                $table->string('dns_secondary')->nullable();
                $table->string('vlan_id')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['active', 'reserved', 'deprecated'])->default('active');
                $table->timestamps();
                
                // Add foreign key to locations if it exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
                }
                
                // Add unique constraint on network/prefix combination
                $table->unique(['network', 'prefix_length']);
            });
        }

        // Check and create IPAM IP addresses table
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address');
                $table->string('hostname')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
                $table->timestamps();
                
                // Add foreign key to subnets
                $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
                
                // Ensure each IP is unique within a subnet
                $table->unique(['subnet_id', 'ip_address']);
            });
        }

        // Check and create IPAM IP assignments table (links IPs to devices)
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                $table->increments('id');
                $table->integer('ip_address_id')->unsigned();
                $table->enum('device_type', ['server', 'switch', 'chassis']);
                $table->integer('device_id')->unsigned();
                $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
                $table->timestamps();
                
                // Add foreign key to IP addresses
                $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
                
                // Ensure one IP can't be assigned multiple times to the same interface
                $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
            });
        }

        // Add foreign keys if tables exist but keys are missing
        dcim_add_missing_foreign_keys();

        // Populate default data
        dcim_populate_default_cpu_models();
        dcim_populate_default_ram_configs();
        dcim_populate_default_switch_models();
        dcim_populate_default_chassis_models();

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating tables - " . $e->getMessage());
        return false;
    }
}

/**
 * Add missing foreign keys if tables exist but foreign keys are missing
 */
function dcim_add_missing_foreign_keys() {
    try {
        // Check if racks table has foreign key to locations
        if (Capsule::schema()->hasTable('dcim_racks') && 
            Capsule::schema()->hasTable('dcim_locations')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

        // Check if servers table has foreign key to racks
        if (Capsule::schema()->hasTable('dcim_servers') && 
            Capsule::schema()->hasTable('dcim_racks')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

    } catch (Exception $e) {
        error_log("DCIM: Error adding foreign keys - " . $e->getMessage());
    }
}

/**
 * Populate default CPU models
 */
function dcim_populate_default_cpu_models() {
    try {
        // Check if CPU models already exist
        $existing_count = Capsule::table('dcim_cpu_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $cpu_models = [
            ['name' => 'Intel Xeon E3-1230v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.5GHz'],
            ['name' => 'Intel Xeon E3-1270v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.8GHz'],
            ['name' => 'Intel Xeon E5-2620v4', 'manufacturer' => 'Intel', 'cores' => '8', 'frequency' => '2.1GHz'],
            ['name' => 'Intel Xeon E5-2630v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.2GHz'],
            ['name' => 'Intel Xeon E5-2640v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.4GHz'],
            ['name' => 'Intel Xeon Gold 5218', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.3GHz'],
            ['name' => 'Intel Xeon Gold 6242', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.8GHz'],
            ['name' => 'Intel Xeon Platinum 8280', 'manufacturer' => 'Intel', 'cores' => '28', 'frequency' => '2.7GHz'],
            ['name' => 'AMD EPYC 7302', 'manufacturer' => 'AMD', 'cores' => '16', 'frequency' => '3.0GHz'],
            ['name' => 'AMD EPYC 7402', 'manufacturer' => 'AMD', 'cores' => '24', 'frequency' => '2.8GHz'],
            ['name' => 'AMD EPYC 7502', 'manufacturer' => 'AMD', 'cores' => '32', 'frequency' => '2.5GHz'],
        ];

        foreach ($cpu_models as $cpu) {
            $cpu['created_at'] = date('Y-m-d H:i:s');
            $cpu['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_cpu_models')->insert($cpu);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating CPU models - " . $e->getMessage());
    }
}

/**
 * Populate default RAM configurations
 */
function dcim_populate_default_ram_configs() {
    try {
        // Check if RAM configs already exist
        $existing_count = Capsule::table('dcim_ram_configs')->count();
        if ($existing_count > 0) {
            return;
        }

        $ram_configs = [
            ['name' => '16GB DDR4', 'size' => '16GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '32GB DDR4', 'size' => '32GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '64GB DDR4', 'size' => '64GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '128GB DDR4', 'size' => '128GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '256GB DDR4', 'size' => '256GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '512GB DDR4', 'size' => '512GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '1TB DDR4', 'size' => '1TB', 'type' => 'DDR4', 'speed' => '3200MHz'],
        ];

        foreach ($ram_configs as $ram) {
            $ram['created_at'] = date('Y-m-d H:i:s');
            $ram['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_ram_configs')->insert($ram);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating RAM configs - " . $e->getMessage());
    }
}

/**
 * Populate default switch models
 */
function dcim_populate_default_switch_models() {
    try {
        // Check if switch models already exist
        $existing_count = Capsule::table('dcim_switch_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $switch_models = [
            ['name' => 'Cisco Catalyst 2960', 'manufacturer' => 'Cisco', 'model_number' => '2960', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3560', 'manufacturer' => 'Cisco', 'model_number' => '3560', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3750', 'manufacturer' => 'Cisco', 'model_number' => '3750', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Cisco Nexus 9300', 'manufacturer' => 'Cisco', 'model_number' => '9300', 'ports' => 48, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'HP ProCurve 2920', 'manufacturer' => 'HP', 'model_number' => '2920', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'HP ProCurve 5400', 'manufacturer' => 'HP', 'model_number' => '5400', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Juniper EX2300', 'manufacturer' => 'Juniper', 'model_number' => 'EX2300', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Juniper EX4300', 'manufacturer' => 'Juniper', 'model_number' => 'EX4300', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Arista 7050S', 'manufacturer' => 'Arista', 'model_number' => '7050S', 'ports' => 52, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'Dell PowerSwitch N1548', 'manufacturer' => 'Dell', 'model_number' => 'N1548', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
        ];

        foreach ($switch_models as $switch) {
            $switch['created_at'] = date('Y-m-d H:i:s');
            $switch['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_switch_models')->insert($switch);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating switch models - " . $e->getMessage());
    }
}

/**
 * Populate default chassis models
 */
function dcim_populate_default_chassis_models() {
    try {
        // Check if chassis models already exist
        $existing_count = Capsule::table('dcim_chassis_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $chassis_models = [
            ['name' => 'Dell PowerEdge M1000e', 'manufacturer' => 'Dell'],
            ['name' => 'HP BladeSystem c7000', 'manufacturer' => 'HP'],
            ['name' => 'IBM BladeCenter H', 'manufacturer' => 'IBM'],
            ['name' => 'Cisco UCS 5108', 'manufacturer' => 'Cisco'],
            ['name' => 'Supermicro SuperBlade', 'manufacturer' => 'Supermicro'],
            ['name' => 'Dell PowerEdge VRTX', 'manufacturer' => 'Dell'],
            ['name' => 'HP ProLiant BL460c', 'manufacturer' => 'HP'],
            ['name' => 'NetApp FAS8200', 'manufacturer' => 'NetApp'],
            ['name' => 'EMC VNX5400', 'manufacturer' => 'EMC'],
            ['name' => 'Hitachi VSP G400', 'manufacturer' => 'Hitachi'],
        ];

        foreach ($chassis_models as $chassis) {
            $chassis['created_at'] = date('Y-m-d H:i:s');
            $chassis['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_chassis_models')->insert($chassis);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating chassis models - " . $e->getMessage());
    }
}

/**
 * Get CPU models from database
 */
function dcim_get_cpu_models() {
    try {
        return Capsule::table('dcim_cpu_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching CPU models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get RAM configurations from database
 */
function dcim_get_ram_configs() {
    try {
        return Capsule::table('dcim_ram_configs')
            ->where('active', true)
            ->orderBy('size')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching RAM configs - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get switch models from database
 */
function dcim_get_switch_models() {
    try {
        return Capsule::table('dcim_switch_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching switch models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get chassis models from database
 */
function dcim_get_chassis_models() {
    try {
        return Capsule::table('dcim_chassis_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching chassis models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Handle AJAX requests for adding CPU models and RAM configs
 */
function dcim_handle_ajax_requests() {
    if ($_POST['ajax_action'] === 'add_cpu_model') {
        try {
            $id = Capsule::table('dcim_cpu_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'cores' => $_POST['cores'] ?? null,
                'frequency' => $_POST['frequency'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_ram_config') {
        try {
            $id = Capsule::table('dcim_ram_configs')->insertGetId([
                'name' => $_POST['name'],
                'size' => $_POST['size'] ?? null,
                'type' => $_POST['type'] ?? null,
                'speed' => $_POST['speed'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_switch_model') {
        try {
            $id = Capsule::table('dcim_switch_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_chassis_model') {
        try {
            $id = Capsule::table('dcim_chassis_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

/**
 * ========================================
 * IPAM (IP Address Management) Functions
 * ========================================
 */

/**
 * Convert IP address to long integer for sorting/calculation
 */
function dcim_ip_to_long($ip) {
    return ip2long($ip);
}

/**
 * Convert long integer back to IP address
 */
function dcim_long_to_ip($long) {
    return long2ip($long);
}

/**
 * Calculate network address from IP and prefix length
 */
function dcim_calculate_network($ip, $prefix_length) {
    $ip_long = dcim_ip_to_long($ip);
    $mask = ~((1 << (32 - $prefix_length)) - 1);
    return dcim_long_to_ip($ip_long & $mask);
}

/**
 * Calculate broadcast address from network and prefix length
 */
function dcim_calculate_broadcast($network, $prefix_length) {
    $network_long = dcim_ip_to_long($network);
    $host_bits = 32 - $prefix_length;
    $broadcast_long = $network_long | ((1 << $host_bits) - 1);
    return dcim_long_to_ip($broadcast_long);
}

/**
 * Calculate total IP addresses in a subnet
 */
function dcim_calculate_total_ips($prefix_length) {
    return pow(2, 32 - $prefix_length);
}

/**
 * Calculate usable IP addresses in a subnet (excluding network and broadcast)
 */
function dcim_calculate_usable_ips($prefix_length) {
    $total = dcim_calculate_total_ips($prefix_length);
    return max(0, $total - 2); // Subtract network and broadcast addresses
}

/**
 * Validate IP address format
 */
function dcim_validate_ip($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
}

/**
 * Validate CIDR notation
 */
function dcim_validate_cidr($cidr) {
    if (!preg_match('/^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\/(\d{1,2})$/', $cidr, $matches)) {
        return false;
    }
    
    $ip = $matches[1];
    $prefix = intval($matches[2]);
    
    return dcim_validate_ip($ip) && $prefix >= 0 && $prefix <= 32;
}

/**
 * Check if an IP address is within a subnet
 */
function dcim_ip_in_subnet($ip, $network, $prefix_length) {
    $ip_long = dcim_ip_to_long($ip);
    $network_long = dcim_ip_to_long($network);
    $mask = ~((1 << (32 - $prefix_length)) - 1);
    
    return ($ip_long & $mask) === ($network_long & $mask);
}

/**
 * Create a new subnet
 */
function dcim_create_subnet($name, $network, $prefix_length, $location_id = null, $gateway = null, $dns_primary = null, $dns_secondary = null, $vlan_id = null, $description = null) {
    try {
        // Validate network address
        if (!dcim_validate_ip($network)) {
            return ['success' => false, 'error' => 'Invalid network address'];
        }
        
        // Validate prefix length
        if ($prefix_length < 0 || $prefix_length > 32) {
            return ['success' => false, 'error' => 'Invalid prefix length'];
        }
        
        // Calculate actual network address
        $actual_network = dcim_calculate_network($network, $prefix_length);
        
        // Check if subnet already exists
        $existing = Capsule::table('dcim_subnets')
            ->where('network', $actual_network)
            ->where('prefix_length', $prefix_length)
            ->first();
            
        if ($existing) {
            return ['success' => false, 'error' => 'Subnet already exists'];
        }
        
        // Create subnet
        $subnet_id = Capsule::table('dcim_subnets')->insertGetId([
            'name' => $name,
            'network' => $actual_network,
            'prefix_length' => $prefix_length,
            'location_id' => $location_id,
            'gateway' => $gateway,
            'dns_primary' => $dns_primary,
            'dns_secondary' => $dns_secondary,
            'vlan_id' => $vlan_id,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // Generate IP addresses for the subnet
        dcim_generate_ip_addresses($subnet_id, $actual_network, $prefix_length);
        
        return ['success' => true, 'subnet_id' => $subnet_id];
        
    } catch (Exception $e) {
        error_log("DCIM: Error creating subnet - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Generate IP addresses for a subnet
 */
function dcim_generate_ip_addresses($subnet_id, $network, $prefix_length) {
    try {
        $network_long = dcim_ip_to_long($network);
        $total_ips = dcim_calculate_total_ips($prefix_length);
        
        $ip_records = [];
        for ($i = 0; $i < $total_ips; $i++) {
            $ip_long = $network_long + $i;
            $ip_address = dcim_long_to_ip($ip_long);
            
            // Determine status based on IP type
            $status = 'available';
            if ($i === 0) {
                $status = 'reserved'; // Network address
            } elseif ($i === $total_ips - 1 && $prefix_length < 31) {
                $status = 'reserved'; // Broadcast address
            }
            
            $ip_records[] = [
                'subnet_id' => $subnet_id,
                'ip_address' => $ip_address,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // Insert in batches for better performance
        $chunks = array_chunk($ip_records, 100);
        foreach ($chunks as $chunk) {
            Capsule::table('dcim_ip_addresses')->insert($chunk);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("DCIM: Error generating IP addresses - " . $e->getMessage());
        return false;
    }
}

/**
 * Divide a subnet into smaller subnets
 */
function dcim_divide_subnet($subnet_id, $new_prefix_length) {
    try {
        // Get parent subnet
        $parent_subnet = Capsule::table('dcim_subnets')->where('id', $subnet_id)->first();
        if (!$parent_subnet) {
            return ['success' => false, 'error' => 'Subnet not found'];
        }
        
        // Validate new prefix length
        if ($new_prefix_length <= $parent_subnet->prefix_length || $new_prefix_length > 30) {
            return ['success' => false, 'error' => 'Invalid new prefix length'];
        }
        
        // Calculate number of new subnets
        $prefix_diff = $new_prefix_length - $parent_subnet->prefix_length;
        $num_subnets = pow(2, $prefix_diff);
        $subnet_size = dcim_calculate_total_ips($new_prefix_length);
        
        $parent_network_long = dcim_ip_to_long($parent_subnet->network);
        $created_subnets = [];
        
        // Create new subnets
        for ($i = 0; $i < $num_subnets; $i++) {
            $subnet_network_long = $parent_network_long + ($i * $subnet_size);
            $subnet_network = dcim_long_to_ip($subnet_network_long);
            
            $new_subnet_id = Capsule::table('dcim_subnets')->insertGetId([
                'name' => $parent_subnet->name . ' - Subnet ' . ($i + 1),
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length,
                'location_id' => $parent_subnet->location_id,
                'gateway' => null, // Will need to be set manually
                'dns_primary' => $parent_subnet->dns_primary,
                'dns_secondary' => $parent_subnet->dns_secondary,
                'vlan_id' => $parent_subnet->vlan_id,
                'description' => "Created by dividing {$parent_subnet->name}",
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            // Generate IP addresses for new subnet
            dcim_generate_ip_addresses($new_subnet_id, $subnet_network, $new_prefix_length);
            
            $created_subnets[] = [
                'id' => $new_subnet_id,
                'network' => $subnet_network,
                'prefix_length' => $new_prefix_length
            ];
        }
        
        // Mark parent subnet as deprecated
        Capsule::table('dcim_subnets')
            ->where('id', $subnet_id)
            ->update(['status' => 'deprecated', 'updated_at' => date('Y-m-d H:i:s')]);
        
        return ['success' => true, 'created_subnets' => $created_subnets];
        
    } catch (Exception $e) {
        error_log("DCIM: Error dividing subnet - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Get available IP addresses in a subnet
 */
function dcim_get_available_ips($subnet_id, $limit = 50) {
    try {
        return Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $subnet_id)
            ->where('status', 'available')
            ->limit($limit)
            ->orderByRaw('INET_ATON(ip_address)')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching available IPs - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Assign IP address to a device
 */
function dcim_assign_ip($ip_address_id, $device_type, $device_id, $interface_type = 'primary') {
    try {
        // Check if IP is available
        $ip = Capsule::table('dcim_ip_addresses')->where('id', $ip_address_id)->first();
        if (!$ip || $ip->status !== 'available') {
            return ['success' => false, 'error' => 'IP address not available'];
        }
        
        // Create assignment
        $assignment_id = Capsule::table('dcim_ip_assignments')->insertGetId([
            'ip_address_id' => $ip_address_id,
            'device_type' => $device_type,
            'device_id' => $device_id,
            'interface_type' => $interface_type,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // Mark IP as assigned
        Capsule::table('dcim_ip_addresses')
            ->where('id', $ip_address_id)
            ->update(['status' => 'assigned', 'updated_at' => date('Y-m-d H:i:s')]);
            
        // Update device IP field based on device type and interface
        dcim_update_device_ip($device_type, $device_id, $interface_type, $ip->ip_address);
        
        return ['success' => true, 'assignment_id' => $assignment_id];
        
    } catch (Exception $e) {
        error_log("DCIM: Error assigning IP - " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update device IP field in the corresponding table
 */
function dcim_update_device_ip($device_type, $device_id, $interface_type, $ip_address) {
    try {
        $table = 'dcim_' . $device_type . 's';
        $field = ($interface_type === 'management') ? 'management_ip' : 'ip_address';
        
        Capsule::table($table)
            ->where('id', $device_id)
            ->update([$field => $ip_address, 'updated_at' => date('Y-m-d H:i:s')]);
            
    } catch (Exception $e) {
        error_log("DCIM: Error updating device IP - " . $e->getMessage());
    }
}

/**
 * Get subnet utilization statistics
 */
function dcim_get_subnet_stats($subnet_id) {
    try {
        $stats = Capsule::table('dcim_ip_addresses')
            ->where('subnet_id', $subnet_id)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "available" THEN 1 ELSE 0 END) as available,
                SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned,
                SUM(CASE WHEN status = "reserved" THEN 1 ELSE 0 END) as reserved
            ')
            ->first();
            
        $stats->used_percentage = $stats->total > 0 ? round(($stats->assigned / $stats->total) * 100, 2) : 0;
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("DCIM: Error fetching subnet stats - " . $e->getMessage());
        return null;
    }
}

/**
 * ========================================
 * IPAM Management Interface Functions
 * ========================================
 */

/**
 * Main IPAM management router
 */
function dcim_manage_ipam($modulelink) {
    $subaction = $_GET['subaction'] ?? 'dashboard';
    
    switch ($subaction) {
        case 'subnets':
            dcim_manage_subnets($modulelink);
            break;
        case 'ips':
            dcim_manage_ip_addresses($modulelink);
            break;
        case 'allocation':
            dcim_manage_ip_allocation($modulelink);
            break;
        case 'dashboard':
        default:
            dcim_ipam_dashboard($modulelink);
            break;
    }
}

/**
 * IPAM Dashboard
 */
function dcim_ipam_dashboard($modulelink) {
    // Get IPAM statistics
    try {
        $total_subnets = Capsule::table('dcim_subnets')->where('status', 'active')->count();
        $total_ips = Capsule::table('dcim_ip_addresses')->count();
        $assigned_ips = Capsule::table('dcim_ip_addresses')->where('status', 'assigned')->count();
        $available_ips = Capsule::table('dcim_ip_addresses')->where('status', 'available')->count();
        $reserved_ips = Capsule::table('dcim_ip_addresses')->where('status', 'reserved')->count();
        
        // Get recent subnet activity
        $recent_subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->orderBy('dcim_subnets.created_at', 'desc')
            ->limit(5)
            ->get();
            
        // Get subnet utilization data
        $subnet_utilization = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_ip_addresses', 'dcim_subnets.id', '=', 'dcim_ip_addresses.subnet_id')
            ->selectRaw('
                dcim_subnets.id,
                dcim_subnets.name,
                dcim_subnets.network,
                dcim_subnets.prefix_length,
                COUNT(dcim_ip_addresses.id) as total_ips,
                SUM(CASE WHEN dcim_ip_addresses.status = "assigned" THEN 1 ELSE 0 END) as assigned_ips
            ')
            ->where('dcim_subnets.status', 'active')
            ->groupBy('dcim_subnets.id', 'dcim_subnets.name', 'dcim_subnets.network', 'dcim_subnets.prefix_length')
            ->get();
            
    } catch (Exception $e) {
        error_log("DCIM: Error fetching IPAM stats - " . $e->getMessage());
        $total_subnets = $total_ips = $assigned_ips = $available_ips = $reserved_ips = 0;
        $recent_subnets = collect([]);
        $subnet_utilization = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">';
    echo '<i class="fas fa-network-wired" style="margin-right: 12px; color: #059669;"></i>';
    echo 'IPAM Dashboard';
    echo '</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showAddSubnetModal()">';
    echo '<i class="fas fa-plus"></i> Add Subnet';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Statistics Overview
    echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #e0f2fe; color: #0277bd;"><i class="fas fa-sitemap"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . $total_subnets . '</div>';
    echo '<div class="stats-label">Active Subnets</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #f3e5f5; color: #7b1fa2;"><i class="fas fa-list-ol"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($total_ips) . '</div>';
    echo '<div class="stats-label">Total IP Addresses</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #e8f5e8; color: #2e7d32;"><i class="fas fa-check-circle"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($assigned_ips) . '</div>';
    echo '<div class="stats-label">Assigned IPs</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="stats-card">';
    echo '<div class="stats-icon" style="background: #fff3e0; color: #ef6c00;"><i class="fas fa-circle"></i></div>';
    echo '<div class="stats-content">';
    echo '<div class="stats-number">' . number_format($available_ips) . '</div>';
    echo '<div class="stats-label">Available IPs</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    
    // Subnet Utilization Chart
    if (count($subnet_utilization) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 24px; margin-bottom: 24px;">';
        echo '<h3 style="margin: 0 0 20px 0; color: #111827; font-weight: 600;">Subnet Utilization</h3>';
        echo '<div class="subnet-utilization-grid">';
        
        foreach ($subnet_utilization as $subnet) {
            $utilization = $subnet->total_ips > 0 ? round(($subnet->assigned_ips / $subnet->total_ips) * 100, 1) : 0;
            $color = $utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981');
            
            echo '<div class="subnet-util-item">';
            echo '<div class="subnet-name">' . htmlspecialchars($subnet->name) . '</div>';
            echo '<div class="subnet-network">' . $subnet->network . '/' . $subnet->prefix_length . '</div>';
            echo '<div class="utilization-bar">';
            echo '<div class="utilization-fill" style="width: ' . $utilization . '%; background: ' . $color . ';"></div>';
            echo '</div>';
            echo '<div class="utilization-text" style="color: ' . $color . ';">' . $utilization . '% (' . $subnet->assigned_ips . '/' . $subnet->total_ips . ')</div>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
    }
    
    // Recent Subnets
    if (count($recent_subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Recent Subnets</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Name</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Network</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Gateway</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($recent_subnets as $subnet) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px; font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->name) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . $subnet->network . '/' . $subnet->prefix_length . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($subnet->location_name ? htmlspecialchars($subnet->location_name) : 'Global') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . ($subnet->gateway ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = ['active' => '#10b981', 'reserved' => '#f59e0b', 'deprecated' => '#6b7280'];
            $color = $status_colors[$subnet->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">' . ucfirst($subnet->status) . '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnet(' . $subnet->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Add CSS for IPAM Dashboard
    echo '<style>
    .stats-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.2s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        flex-shrink: 0;
    }
    
    .stats-content {
        flex: 1;
    }
    
    .stats-number {
        font-size: 32px;
        font-weight: 700;
        color: #111827;
        line-height: 1;
        margin-bottom: 4px;
    }
    
    .stats-label {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
    }
    
    .subnet-utilization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .subnet-util-item {
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: #f9fafb;
    }
    
    .subnet-name {
        font-weight: 600;
        color: #111827;
        margin-bottom: 4px;
    }
    
    .subnet-network {
        font-family: monospace;
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 12px;
    }
    
    .utilization-bar {
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;
    }
    
    .utilization-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .utilization-text {
        font-size: 12px;
        font-weight: 500;
    }
    </style>';
    
    // Add JavaScript
    echo '<script>
    function viewSubnet(subnetId) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets&view=" + subnetId;
    }
    
    function showAddSubnetModal() {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets";
    }
    </script>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
}

/**
 * Manage Subnets
 */
function dcim_manage_subnets($modulelink) {
    // Handle form submissions
    if ($_POST['action'] == 'add_subnet') {
        $result = dcim_create_subnet(
            $_POST['name'],
            $_POST['network'],
            $_POST['prefix_length'],
            $_POST['location_id'] ?: null,
            $_POST['gateway'] ?: null,
            $_POST['dns_primary'] ?: null,
            $_POST['dns_secondary'] ?: null,
            $_POST['vlan_id'] ?: null,
            $_POST['description'] ?: null
        );
        
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet created successfully!</div>';
        } else {
            echo '<div class="alert alert-danger">Error creating subnet: ' . $result['error'] . '</div>';
        }
    }
    
    if ($_POST['action'] == 'divide_subnet') {
        $result = dcim_divide_subnet($_POST['subnet_id'], $_POST['new_prefix_length']);
        
        if ($result['success']) {
            echo '<div class="alert alert-success">Subnet divided successfully! Created ' . count($result['created_subnets']) . ' new subnets.</div>';
        } else {
            echo '<div class="alert alert-danger">Error dividing subnet: ' . $result['error'] . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_subnets')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Subnet deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting subnet: ' . $e->getMessage() . '</div>';
        }
    }
    
    // Get data
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $subnets = Capsule::table('dcim_subnets')
            ->leftJoin('dcim_locations', 'dcim_subnets.location_id', '=', 'dcim_locations.id')
            ->select('dcim_subnets.*', 'dcim_locations.name as location_name')
            ->orderBy('dcim_subnets.created_at', 'desc')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
        $subnets = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Subnet Management</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add subnet form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Subnet</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_subnet">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Subnet Name *</label>';
    echo '<input type="text" name="name" required placeholder="e.g., Production Network" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Network Address *</label>';
    echo '<input type="text" name="network" required placeholder="e.g., ***********" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Prefix Length *</label>';
    echo '<select name="prefix_length" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    for ($i = 8; $i <= 30; $i++) {
        $hosts = pow(2, 32 - $i);
        echo '<option value="' . $i . '">/' . $i . ' (' . number_format($hosts) . ' addresses)</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location</label>';
    echo '<select name="location_id" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="">Global (No specific location)</option>';
    foreach ($locations as $location) {
        echo '<option value="' . $location->id . '">' . htmlspecialchars($location->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Gateway</label>';
    echo '<input type="text" name="gateway" placeholder="e.g., ***********" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Primary DNS</label>';
    echo '<input type="text" name="dns_primary" placeholder="e.g., 8.8.8.8" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Secondary DNS</label>';
    echo '<input type="text" name="dns_secondary" placeholder="e.g., 8.8.4.4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">VLAN ID</label>';
    echo '<input type="text" name="vlan_id" placeholder="e.g., 100" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<textarea name="description" rows="3" placeholder="Optional description..." style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Create Subnet';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing subnets
    if (count($subnets) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Subnets</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Name</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Network</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Gateway</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($subnets as $subnet) {
            // Get utilization stats
            $stats = dcim_get_subnet_stats($subnet->id);
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($subnet->name) . '</div>';
            if ($subnet->description) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . htmlspecialchars($subnet->description) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-family: monospace; color: #111827;">' . $subnet->network . '/' . $subnet->prefix_length . '</div>';
            if ($stats) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . number_format($stats->total) . ' total IPs</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($subnet->location_name ? htmlspecialchars($subnet->location_name) : 'Global') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280; font-family: monospace;">' . ($subnet->gateway ?: '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = ['active' => '#10b981', 'reserved' => '#f59e0b', 'deprecated' => '#6b7280'];
            $color = $status_colors[$subnet->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">' . ucfirst($subnet->status) . '</span>';
            if ($stats && $stats->total > 0) {
                echo '<div style="font-size: 12px; color: #6b7280; margin-top: 4px;">' . $stats->used_percentage . '% used</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewSubnetIPs(' . $subnet->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-list"></i> IPs';
            echo '</button>';
            echo '<button onclick="divideSubnet(' . $subnet->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-cut"></i> Divide';
            echo '</button>';
            echo '<button onclick="deleteSubnet(' . $subnet->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '&action=ipam";
    }
    
    function viewSubnetIPs(subnetId) {
        window.location.href = "' . $modulelink . '&action=ipam&subaction=ips&subnet_id=" + subnetId;
    }
    
    function divideSubnet(subnetId) {
        const newPrefix = prompt("Enter new prefix length (must be larger than current):");
        if (newPrefix && !isNaN(newPrefix)) {
            const form = document.createElement("form");
            form.method = "post";
            form.innerHTML = `
                <input type="hidden" name="action" value="divide_subnet">
                <input type="hidden" name="subnet_id" value="${subnetId}">
                <input type="hidden" name="new_prefix_length" value="${newPrefix}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function deleteSubnet(subnetId) {
        if (confirm("Are you sure you want to delete this subnet and all its IP addresses?")) {
            window.location.href = "' . $modulelink . '&action=ipam&subaction=subnets&delete=" + subnetId;
        }
    }
    </script>';
}

/**
 * Placeholder functions for other IPAM interfaces
 */
function dcim_manage_ip_addresses($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Address Management</div>';
}

function dcim_manage_ip_allocation($modulelink) {
    echo '<div class="dcim-container">Coming soon: IP Allocation Interface</div>';
}

/**
 * Add-on activation
 */
function dcim_activate() {
    // Ensure tables exist
    if (dcim_ensure_tables_exist()) {
        return ["status" => "success", "description" => "DCIM add-on activated successfully. Database tables created."];
    } else {
        return ["status" => "error", "description" => "DCIM add-on activation failed. Could not create database tables."];
    }
}

/**
 * Add-on deactivation
 */
function dcim_deactivate() {
    // Tables are preserved on deactivation
    return ["status" => "success", "description" => "DCIM add-on deactivated successfully."];
}

/**
 * Add-on upgrade
 */
function dcim_upgrade($vars) {
    $version = $vars['version'];
    // Handle version-specific upgrades here
    return ["status" => "success", "description" => "DCIM add-on upgraded successfully."];
}

/**
 * Admin area output
 */
function dcim_output($vars) {
    // IMMEDIATE DEBUG - Write to a file we know we can access
    file_put_contents('/tmp/dcim_debug.log', date('Y-m-d H:i:s') . " - dcim_output called\n", FILE_APPEND);
    
    $action = $_GET['action'] ?? 'dashboard';
    $modulelink = $vars['modulelink'];
    
    // Debug logging
    error_log("DCIM: dcim_output called with action: $action");
    error_log("DCIM: modulelink: $modulelink");
    error_log("DCIM: GET params: " . print_r($_GET, true));
    error_log("DCIM: POST params: " . print_r($_POST, true));
    error_log("DCIM: Request method: " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: Is AJAX request: " . (isset($_GET['ajax']) ? 'YES' : 'NO'));
    
    // Also write to our debug file
    file_put_contents('/tmp/dcim_debug.log', date('Y-m-d H:i:s') . " - Action: $action, AJAX: " . (isset($_GET['ajax']) ? 'YES' : 'NO') . "\n", FILE_APPEND);
    
    // Handle AJAX requests first
    if (isset($_POST['ajax_action'])) {
        dcim_handle_ajax_requests();
        return;
    }
    
    // Ensure database tables exist before any operations
    if (!dcim_ensure_tables_exist()) {
        echo '<div class="alert alert-danger">';
        echo '<h4>Database Error</h4>';
        echo '<p>Could not create or access DCIM database tables. Please check your database permissions and try again.</p>';
        echo '<p>You may need to manually activate the addon or contact your system administrator.</p>';
        echo '</div>';
        return;
    }

    // Handle sample data creation
    if (dcim_handle_sample_data($modulelink)) {
        return; // Sample data was processed, stop here
    }
    
    // Include FontAwesome with integrity check
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Embed CSS directly to avoid WHMCS restrictions
    echo '<style>
    /* DCIM Modern Interface - Embedded CSS */
    
    /* Reset and Base */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Override WHMCS default styles */
    .dcim-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
        background: #f8fafc !important;
        margin: 0 !important;
        padding: 0 !important;
        min-height: 100vh !important;
        width: 100% !important;
        color: #1a202c !important;
        line-height: 1.5 !important;
    }

    .dcim-container * {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
    }

    /* Main Layout */
    .dcim-layout {
        display: flex !important;
        min-height: 100vh !important;
        background: #ffffff !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    }

    /* Sidebar */
    .dcim-sidebar {
        width: 280px !important;
        background: #ffffff !important;
        border-right: 1px solid #e2e8f0 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    /* Sidebar Header */
    .sidebar-header {
        padding: 24px 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        background: #ffffff !important;
    }

    .sidebar-title {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 4px 0 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
    }

    .last-updated {
        font-size: 12px !important;
        color: #718096 !important;
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        font-weight: 400 !important;
    }

    .last-updated i {
        font-size: 11px !important;
    }

    /* Add Location Button */
    .add-location-btn {
        background: #4299e1 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 8px 12px !important;
        font-size: 13px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        transition: all 0.2s ease !important;
        margin-top: 12px !important;
        width: 100% !important;
        justify-content: center !important;
        text-decoration: none !important;
    }

    .add-location-btn:hover {
        background: #3182ce !important;
        transform: translateY(-1px) !important;
    }

    .add-location-btn i {
        font-size: 12px !important;
    }

    /* Search Container */
    .search-container {
        padding: 16px 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
    }

    .search-box {
        position: relative !important;
        display: flex !important;
        align-items: center !important;
    }

    .search-input {
        width: 100% !important;
        padding: 8px 12px 8px 32px !important;
        border: 1px solid #cbd5e0 !important;
        border-radius: 6px !important;
        font-size: 13px !important;
        background: #f7fafc !important;
        transition: all 0.2s ease !important;
        color: #2d3748 !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #4299e1 !important;
        background: #ffffff !important;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1) !important;
    }

    .search-input::placeholder {
        color: #a0aec0 !important;
    }

    .search-icon {
        position: absolute !important;
        left: 10px !important;
        color: #a0aec0 !important;
        font-size: 13px !important;
        z-index: 2 !important;
    }

    .add-search-btn {
        position: absolute !important;
        right: 6px !important;
        background: #4299e1 !important;
        color: white !important;
        border: none !important;
        border-radius: 4px !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        font-size: 11px !important;
    }

    .add-search-btn:hover {
        background: #3182ce !important;
    }

    /* Locations List */
    .locations-list {
        flex: 1 !important;
        overflow-y: auto !important;
        padding: 8px 0 !important;
    }

    .location-item {
        padding: 12px 20px !important;
        cursor: pointer !important;
        border-bottom: 1px solid #f1f5f9 !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        position: relative !important;
    }

    .location-item:hover {
        background: #f7fafc !important;
    }

    .location-item.active {
        background: #ebf8ff !important;
        border-right: 3px solid #4299e1 !important;
    }

    .location-flag {
        width: 20px !important;
        height: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        flex-shrink: 0 !important;
    }

    .location-info {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .location-name {
        font-weight: 500 !important;
        color: #2d3748 !important;
        font-size: 14px !important;
        margin: 0 !important;
        line-height: 1.3 !important;
    }

    .location-actions {
        display: flex !important;
        gap: 4px !important;
        opacity: 0 !important;
        transition: opacity 0.2s ease !important;
    }

    .location-item:hover .location-actions {
        opacity: 1 !important;
    }

    .action-btn {
        background: none !important;
        border: none !important;
        color: #6b7280 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 4px !important;
        transition: all 0.2s ease !important;
        font-size: 12px !important;
        min-width: auto !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 28px !important;
        height: 28px !important;
        margin: 0 2px !important;
    }

    .action-btn:hover {
        background: #f3f4f6 !important;
        color: #374151 !important;
        transform: scale(1.1) !important;
    }

    .action-btn i {
        font-size: 13px !important;
        line-height: 1 !important;
        color: inherit !important;
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        display: block !important;
        width: 100% !important;
        text-align: center !important;
    }

    .action-btn i.fa-eye {
        color: #3b82f6 !important;
    }

    .action-btn i.fa-edit {
        color: #10b981 !important;
    }

    .action-btn i.fa-trash {
        color: #ef4444 !important;
    }

    .action-btn i.fa-plus {
        color: #8b5cf6 !important;
    }

    .action-btn i.fa-server,
    .add-location-btn i.fa-server {
        color: inherit !important;
    }

    /* IPAM Menu Styles */
    .sidebar-section {
        display: flex !important;
        flex-direction: column !important;
    }

    .sidebar-stats {
        display: flex !important;
        gap: 8px !important;
        margin-top: 4px !important;
    }

    .ipam-menu {
        padding: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 2px !important;
    }

    .ipam-menu-item {
        padding: 12px 20px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        border: none !important;
        background: none !important;
        color: #374151 !important;
        font-size: 14px !important;
    }

    .ipam-menu-item:hover {
        background: #f7fafc !important;
        color: #059669 !important;
    }

    .ipam-menu-item i {
        width: 20px !important;
        flex-shrink: 0 !important;
        text-align: center !important;
        color: #6b7280 !important;
        font-size: 14px !important;
    }

    .ipam-menu-item:hover i {
        color: #059669 !important;
    }

    .ipam-menu-item span:first-of-type {
        flex: 1 !important;
        font-weight: 500 !important;
    }

    .item-count {
        font-size: 12px !important;
        color: #6b7280 !important;
        background: #f3f4f6 !important;
        padding: 2px 8px !important;
        border-radius: 10px !important;
        font-weight: 400 !important;
    }

    .ipam-menu-item:hover .item-count {
        background: #ecfdf5 !important;
        color: #059669 !important;
    }

    /* New Menu Section Styles */
    .menu-section {
        border-bottom: 1px solid #e5e7eb !important;
        margin-bottom: 8px !important;
    }

    .menu-header {
        padding: 16px 20px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        transition: all 0.2s ease !important;
        background: #ffffff !important;
        border: none !important;
        width: 100% !important;
        text-align: left !important;
    }

    .menu-header:hover {
        background: #f8fafc !important;
    }

    .menu-title {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        flex: 1 !important;
    }

    .menu-title i {
        font-size: 16px !important;
        width: 20px !important;
        text-align: center !important;
    }

    .menu-title span {
        font-size: 15px !important;
        font-weight: 600 !important;
        color: #1f2937 !important;
    }

    .menu-stats {
        display: flex !important;
        gap: 6px !important;
        margin-left: 8px !important;
    }

    .menu-stats span {
        font-size: 10px !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 10px !important;
        font-weight: 500 !important;
        min-width: 20px !important;
        text-align: center !important;
    }

    .toggle-icon {
        font-size: 12px !important;
        color: #6b7280 !important;
        transition: transform 0.2s ease !important;
        transform: rotate(0deg) !important;
    }

    .menu-content {
        max-height: 0 !important;
        overflow: hidden !important;
        transition: max-height 0.3s ease !important;
        background: #fafbfc !important;
    }

    .menu-content.expanded {
        max-height: 600px !important;
    }

    .menu-item {
        padding: 10px 20px 10px 52px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        border: none !important;
        background: none !important;
        color: #374151 !important;
        font-size: 14px !important;
        text-decoration: none !important;
        border-bottom: 1px solid #f1f5f9 !important;
    }

    .menu-item:hover {
        background: #f1f5f9 !important;
        color: #1f2937 !important;
        transform: translateX(4px) !important;
    }

    .menu-item i {
        width: 16px !important;
        flex-shrink: 0 !important;
        text-align: center !important;
        color: #6b7280 !important;
        font-size: 13px !important;
    }

    .menu-item:hover i {
        color: #374151 !important;
    }

    .menu-item span:first-of-type {
        flex: 1 !important;
        font-weight: 500 !important;
    }

    /* Force FontAwesome icons to be visible */
    .action-btn i::before {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        line-height: 1 !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }

    /* Ensure icons dont get overridden */
    .dcim-container .action-btn i {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Fallback if FontAwesome fails to load */
    .action-btn[title="View Racks"] i::before,
    .action-btn[title="View"] i::before {
        content: "👁" !important;
    }
    
    .action-btn[title="Edit"] i::before {
        content: "✏" !important;
    }
    
    .action-btn[title="Delete"] i::before {
        content: "🗑" !important;
    }
    
    .action-btn[title="Add Rack"] i::before {
        content: "+" !important;
    }
    
    /* Fallback for All Servers button */
    .add-location-btn i.fa-server::before {
        content: "🖥" !important;
    }

    /* Main Content Area */
    .dcim-main {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        background: #f8fafc !important;
    }

    /* Main Header */
    .main-header {
        padding: 20px 24px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        background: #ffffff !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }

    .back-btn {
        background: none !important;
        border: none !important;
        color: #718096 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .back-btn:hover {
        background: #f1f5f9 !important;
        color: #4a5568 !important;
    }

    .main-title {
        font-size: 20px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
    }

    /* Rack Visualization Area */
    .rack-visualization {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        padding: 24px !important;
        overflow: auto !important;
    }

    /* Page Section Headers */
    .page-section-header {
        text-align: center !important;
        margin-bottom: 24px !important;
    }

    .page-section-header h3 {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
    }

    /* Rack Cards Grid */
    .racks-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)) !important;
        gap: 16px !important;
        margin-top: 20px !important;
    }

    .rack-card {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .rack-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border-color: #cbd5e0 !important;
    }

    .rack-card-header {
        text-align: center !important;
        margin-bottom: 16px !important;
    }

    .rack-name {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 4px 0 !important;
    }

    .rack-type {
        font-size: 13px !important;
        color: #718096 !important;
        margin: 0 !important;
    }

    .rack-stats {
        font-size: 12px !important;
        color: #a0aec0 !important;
        line-height: 1.4 !important;
        text-align: center !important;
    }

    /* Empty State */
    .empty-state {
        text-align: center !important;
        padding: 60px 20px !important;
        color: #718096 !important;
    }

    .empty-state i {
        font-size: 48px !important;
        margin-bottom: 16px !important;
        opacity: 0.3 !important;
    }

    .empty-state h3 {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #4a5568 !important;
        margin: 0 0 8px 0 !important;
    }

    .empty-state p {
        font-size: 14px !important;
        color: #718096 !important;
        margin: 0 !important;
    }

    /* Stats Panel */
    .stats-panel {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
    }

    .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
        gap: 16px !important;
    }

    .stat-item {
        text-align: center !important;
    }

    .stat-number {
        font-size: 24px !important;
        font-weight: 700 !important;
        color: #4299e1 !important;
        margin: 0 !important;
        line-height: 1 !important;
    }

    .stat-label {
        font-size: 12px !important;
        color: #718096 !important;
        margin: 4px 0 0 0 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    /* Override any WHMCS interference */
    .dcim-container .btn,
    .dcim-container .panel,
    .dcim-container .form-control,
    .dcim-container .table,
    .dcim-container .alert {
        all: unset !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dcim-layout {
            flex-direction: column !important;
        }
        
        .dcim-sidebar {
            width: 100% !important;
            max-height: 200px !important;
            border-right: none !important;
            border-bottom: 1px solid #e2e8f0 !important;
        }
        
        .locations-list {
            max-height: 120px !important;
        }
        
        .racks-grid {
            grid-template-columns: 1fr !important;
            gap: 12px !important;
        }
        
        .main-header {
            padding: 16px 20px !important;
        }
        
        .rack-visualization {
            padding: 16px !important;
        }
    }

    /* Animations */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .location-item,
    .rack-card {
        animation: fadeIn 0.3s ease-out !important;
    }
    </style>';
    

    
    switch ($action) {
        case 'locations':
            dcim_manage_locations($modulelink);
            break;
        case 'racks':
            dcim_manage_racks($modulelink);
            break;
        case 'servers':
            dcim_servers_table($modulelink);
            break;
        case 'servers_manage':
            dcim_manage_servers($modulelink);
            break;
        case 'servers_table':
            dcim_servers_table($modulelink);
            break;
        case 'switches':
            dcim_switches_table($modulelink);
            break;
        case 'switches_manage':
            dcim_manage_switches($modulelink);
            break;
        case 'switches_table':
            dcim_switches_table($modulelink);
            break;
        case 'chassies':
            dcim_chassies_table($modulelink);
            break;
        case 'chassies_manage':
            dcim_manage_chassies($modulelink);
            break;
        case 'chassies_table':
            dcim_chassies_table($modulelink);
            break;
        case 'rack_view':
            dcim_modern_rack_view($modulelink);
            break;
        case 'ipam':
            dcim_manage_ipam($modulelink);
            break;
        default:
            dcim_modern_dashboard($modulelink);
            break;
    }
}

/**
 * Client area output
 */
function dcim_clientarea($vars) {
    if ($vars['enable_client_access'] !== 'on') {
        return ['pagetitle' => 'Access Denied', 'breadcrumb' => ['Access Denied'], 'templatefile' => 'access_denied'];
    }
    
    // Ensure database tables exist
    if (!dcim_ensure_tables_exist()) {
        return [
            'pagetitle' => 'Database Error',
            'breadcrumb' => ['Database Error'],
            'templatefile' => 'error',
            'vars' => [
                'error_message' => 'Database tables are not properly configured. Please contact support.'
            ]
        ];
    }
    
    $client_id = $_SESSION['uid'];
    
    // Get client's servers
    try {
        $servers = Capsule::table('dcim_servers')
            ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_servers.client_id', $client_id)
            ->select('dcim_servers.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM Client Area Error: " . $e->getMessage());
        $servers = collect([]);
    }
    
    return [
        'pagetitle' => 'My Servers',
        'breadcrumb' => ['My Servers'],
        'templatefile' => 'clientarea',
        'vars' => [
            'servers' => $servers,
            'modulelink' => $vars['modulelink']
        ]
    ];
}

/**
 * Dashboard function (legacy - redirects to modern dashboard)
 */
function dcim_dashboard($modulelink) {
    return dcim_modern_dashboard($modulelink);
}

/**
 * Manage locations with modern interface
 */
function dcim_manage_locations($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Handle form submissions
    if ($_POST['action'] == 'add_location') {
        try {
            Capsule::table('dcim_locations')->insert([
                'name' => $_POST['name'],
                'address' => $_POST['address'],
                'city' => $_POST['city'],
                'country' => $_POST['country'],
                'contact_name' => $_POST['contact_name'],
                'contact_email' => $_POST['contact_email'],
                'contact_phone' => $_POST['contact_phone'],
                'total_power_capacity' => $_POST['total_power_capacity'] ?: 0,
                'power_unit' => $_POST['power_unit'],
                'notes' => $_POST['notes'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo '<div class="alert alert-success">Location added successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error adding location: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_locations')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Location deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting location: ' . $e->getMessage() . '</div>';
        }
    }
    
    try {
        $locations = Capsule::table('dcim_locations')->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching locations: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Location Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add location form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;" id="addLocationForm">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Location</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_location">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location Name *</label>';
    echo '<input type="text" name="name" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">City</label>';
    echo '<input type="text" name="city" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Country</label>';
    echo '<input type="text" name="country" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Name</label>';
    echo '<input type="text" name="contact_name" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Email</label>';
    echo '<input type="email" name="contact_email" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Contact Phone</label>';
    echo '<input type="text" name="contact_phone" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Capacity</label>';
    echo '<input type="number" name="total_power_capacity" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Unit</label>';
    echo '<select name="power_unit" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="Watts">Watts</option>';
    echo '<option value="Kilowatts">Kilowatts</option>';
    echo '<option value="Amps">Amps</option>';
    echo '</select>';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Address</label>';
    echo '<textarea name="address" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Notes</label>';
    echo '<textarea name="notes" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Add Location';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing locations
    if (count($locations) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Locations</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Contact</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Power</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Racks</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($locations as $location) {
            $rack_count = 0;
            try {
                $rack_count = Capsule::table('dcim_racks')->where('location_id', $location->id)->count();
            } catch (Exception $e) {
                error_log("DCIM: Error counting racks - " . $e->getMessage());
            }
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($location->name) . '</div>';
            if ($location->city) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($location->city . ', ' . $location->country) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($location->contact_name) {
                echo htmlspecialchars($location->contact_name);
                if ($location->contact_email) {
                    echo '<br><span style="font-size: 12px;">' . htmlspecialchars($location->contact_email) . '</span>';
                }
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($location->total_power_capacity > 0) {
                echo number_format($location->total_power_capacity) . ' ' . $location->power_unit;
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . $rack_count . '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewRacks(' . $location->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '<button onclick="deleteLocation(' . $location->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function viewRacks(locationId) {
        window.location.href = "' . $modulelink . '&location_id=" + locationId;
    }
    
    function showAddForm() {
        document.getElementById("addLocationForm").scrollIntoView();
    }
    </script>';
}

/**
 * Manage racks with modern interface
 */
function dcim_manage_racks($modulelink) {
    $location_filter = $_GET['location_id'] ?? null;
    
    // Handle form submissions
    if ($_POST['action'] == 'add_rack') {
        try {
            Capsule::table('dcim_racks')->insert([
                'location_id' => $_POST['location_id'],
                'name' => $_POST['name'],
                'row' => $_POST['row'],
                'position' => $_POST['position'],
                'units' => $_POST['units'],
                'power_capacity' => $_POST['power_capacity'],
                'pdu_a' => $_POST['pdu_a'],
                'pdu_b' => $_POST['pdu_b'],
                'notes' => $_POST['notes'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo '<div class="alert alert-success">Rack added successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error adding rack: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_racks')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Rack deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting rack: ' . $e->getMessage() . '</div>';
        }
    }
    
    try {
        $locations = Capsule::table('dcim_locations')->get();
        
        $racks_query = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name');
        
        if ($location_filter) {
            $racks_query->where('dcim_racks.location_id', $location_filter);
        }
        
        $racks = $racks_query->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $locations = collect([]);
        $racks = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $location_filter);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="main-title">Rack Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Add rack form
    echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin-bottom: 24px;" id="addRackForm">';
    echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Add New Rack</h3>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_rack">';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Location *</label>';
    echo '<select name="location_id" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '<option value="">Select Location</option>';
    foreach ($locations as $location) {
        $selected = ($location_filter == $location->id) ? 'selected' : '';
        echo '<option value="' . $location->id . '" ' . $selected . '>' . htmlspecialchars($location->name) . '</option>';
    }
    echo '</select>';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Rack Name *</label>';
    echo '<input type="text" name="name" required style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Row</label>';
    echo '<input type="text" name="row" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Position</label>';
    echo '<input type="text" name="position" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Units</label>';
    echo '<input type="number" name="units" value="42" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Power Capacity (W)</label>';
    echo '<input type="number" name="power_capacity" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">PDU A</label>';
    echo '<input type="text" name="pdu_a" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">PDU B</label>';
    echo '<input type="text" name="pdu_b" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;">';
    echo '</div>';
    
    echo '</div>';
    
    echo '<div style="margin-bottom: 24px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Notes</label>';
    echo '<textarea name="notes" rows="3" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; resize: vertical;"></textarea>';
    echo '</div>';
    
    echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
    echo '<i class="fas fa-plus"></i> Add Rack';
    echo '</button>';
    echo '</form>';
    echo '</div>';
    
    // Existing racks
    if (count($racks) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden;">';
        echo '<div style="padding: 24px; border-bottom: 1px solid #e5e7eb; background: #f8fafc;">';
        echo '<h3 style="margin: 0; color: #111827; font-weight: 600;">Existing Racks</h3>';
        echo '</div>';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Capacity</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Utilization</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($racks as $rack) {
            $server_count = 0;
            $used_units = 0;
            try {
                $server_count = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->count();
                $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->sum('unit_size');
            } catch (Exception $e) {
                error_log("DCIM: Error counting servers - " . $e->getMessage());
            }
            
            $utilization = $rack->units > 0 ? round(($used_units / $rack->units) * 100, 1) : 0;
            
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($rack->name) . '</div>';
            if ($rack->row || $rack->position) {
                echo '<div style="font-size: 12px; color: #6b7280;">Row ' . htmlspecialchars($rack->row) . ', Pos ' . htmlspecialchars($rack->position) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($rack->location_name) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . htmlspecialchars($rack->row . ' / ' . $rack->position) . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . $rack->units . 'U</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; align-items: center; gap: 8px;">';
            echo '<div style="flex: 1; background: #f3f4f6; height: 8px; border-radius: 4px; overflow: hidden;">';
            echo '<div style="width: ' . $utilization . '%; height: 100%; background: ' . ($utilization > 80 ? '#ef4444' : ($utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
            echo '</div>';
            echo '<span style="font-size: 12px; color: #6b7280;">' . $utilization . '%</span>';
            echo '</div>';
            echo '<div style="font-size: 12px; color: #9ca3af; margin-top: 4px;">' . $used_units . '/' . $rack->units . 'U used</div>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="viewRack(' . $rack->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-eye"></i> View';
            echo '</button>';
            echo '<button onclick="manageServers(' . $rack->id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-server"></i> Servers';
            echo '</button>';
            echo '<button onclick="deleteRack(' . $rack->id . ')" style="background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-trash"></i>';
            echo '</button>';
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function goBack() {
        window.location.href = "' . $modulelink . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function viewRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function manageServers(rackId) {
        window.location.href = "' . $modulelink . '&action=servers&rack_id=" + rackId;
    }
    
    function deleteRack(rackId) {
        if (confirm("Are you sure you want to delete this rack and all its servers?")) {
            window.location.href = "' . $modulelink . '&action=racks&delete=" + rackId;
        }
    }
    
    function showAddForm() {
        document.getElementById("addRackForm").scrollIntoView();
    }
    
    // Search functionality for rack table
    if (document.getElementById("rackSearch")) {
        document.getElementById("rackSearch").addEventListener("input", function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll("tbody tr");
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = "";
                } else {
                    row.style.display = "none";
                }
            });
        });
    }
    </script>';
}

/**
 * Manage servers with modern bulk interface
 */
function dcim_manage_servers($modulelink) {
    // Debug logging at the very start
    error_log("DCIM: ===== dcim_manage_servers ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    $rack_filter = $_GET['rack_id'] ?? null;
    $edit_server_id = $_GET['edit'] ?? null;
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: rack_filter = " . ($rack_filter ?? 'null'));
    error_log("DCIM: edit_server_id = " . ($edit_server_id ?? 'null'));
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: GET params: " . print_r($_GET, true));
    error_log("DCIM: POST params: " . print_r($_POST, true));
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle device assignment
    if ($_GET['action'] == 'assign_device') {
        $device_id = $_GET['device_id'];
        $rack_id = $_GET['rack_id'];
        $unit = $_GET['unit'];
        
        if ($device_id && $rack_id && $unit) {
            try {
                Capsule::table('dcim_servers')
                    ->where('id', $device_id)
                    ->update([
                        'rack_id' => $rack_id,
                        'start_unit' => $unit,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                
                // Redirect back to rack view
                echo '<script>window.location.href = "' . $modulelink . '&action=rack_view&rack_id=' . $rack_id . '";</script>';
                return;
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">Error assigning device: ' . $e->getMessage() . '</div>';
            }
        }
    }
    
    // Handle bulk server addition
    error_log("DCIM: Checking for bulk server addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_servers') {
        error_log("DCIM: BULK SERVER ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk server addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values (do this once, not per server)
        try {
            Capsule::schema()->table('dcim_servers', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['servers']) || !is_array($_POST['servers'])) {
            error_log("DCIM: No servers data found in POST");
            echo '<div style="color: red;">No server data received. Please fill in at least one server label.</div>';
            return;
        }
        
        foreach ($_POST['servers'] as $index => $server_data) {
            // Check if all required fields are present
            $has_label = !empty(trim($server_data['label'] ?? ''));
            $has_cpu = !empty($server_data['cpu_model'] ?? '');
            $has_ram = !empty($server_data['ram_config'] ?? '');
            $has_city = !empty($server_data['city'] ?? '');
            $has_rack = !empty($server_data['rack_id'] ?? '');
            
            // Skip rows that don't have any meaningful data
            if (!$has_label && !$has_cpu && !$has_ram && !$has_city && !$has_rack) {
                continue;
            }
            
            // Only process servers that have ALL required fields
            if (!$has_label || !$has_cpu || !$has_ram || !$has_city || !$has_rack) {
                error_log("DCIM: Skipping server $index - missing required fields. Label: $has_label, CPU: $has_cpu, RAM: $has_ram, City: $has_city, Rack: $has_rack");
                $error_count++;
                continue;
            }
            
            error_log("DCIM: Processing server $index with label: " . ($server_data['label'] ?? 'empty'));
            
            try {
                // Create a label from user input or use default Device number
                $label = !empty(trim($server_data['label'])) ? trim($server_data['label']) : 'Device ' . ($index + 1);
                
                Capsule::table('dcim_servers')->insert([
                    'name' => $label,
                    'hostname' => $server_data['hostname'] ?? null,
                    'rack_id' => !empty($server_data['rack_id']) ? $server_data['rack_id'] : null,
                    'start_unit' => !empty($server_data['start_unit']) ? $server_data['start_unit'] : null,
                    'unit_size' => !empty($server_data['unit_size']) ? $server_data['unit_size'] : 1,
                    'make' => $server_data['cpu_model'] ?? null,
                    'model' => $server_data['cpu_model'] ?? null,
                    'specifications' => $server_data['ram_config'] ?? null,
                    'ip_address' => $server_data['ipmi_address'] ?? null,
                    'status' => $server_data['status'] ?? 'available',
                    'serial_number' => $server_data['mac_address'] ?? null,
                    'notes' => 'IPMI Password: ' . ($server_data['ipmi_password'] ?? ''),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding server " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count servers");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' server(s) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' servers added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
            echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=servers_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' server(s) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Handle single server update
    if ($_POST['action'] == 'update_server' && $edit_server_id) {
        try {
            Capsule::table('dcim_servers')
                ->where('id', $edit_server_id)
                ->update([
                    'rack_id' => $_POST['rack_id'] ?: null,
                    'name' => $_POST['name'],
                    'hostname' => $_POST['hostname'],
                    'start_unit' => $_POST['start_unit'] ?: null,
                    'unit_size' => $_POST['unit_size'] ?: 1,
                    'make' => $_POST['make'],
                    'model' => $_POST['model'],
                    'serial_number' => $_POST['serial_number'],
                    'specifications' => $_POST['specifications'],
                    'power_consumption' => $_POST['power_consumption'] ?: 0,
                    'ip_address' => $_POST['ip_address'],
                    'client_id' => $_POST['client_id'] ?: null,
                    'service_id' => $_POST['service_id'] ?: null,
                    'notes' => $_POST['notes'],
                    'status' => $_POST['status'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            echo '<div class="alert alert-success">Server updated successfully!</div>';
            $edit_server_id = null; // Reset edit mode
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error updating server: ' . $e->getMessage() . '</div>';
        }
    }
    
    if ($_GET['delete']) {
        try {
            Capsule::table('dcim_servers')->where('id', $_GET['delete'])->delete();
            echo '<div class="alert alert-success">Server deleted successfully!</div>';
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">Error deleting server: ' . $e->getMessage() . '</div>';
        }
    }
    
    // Get data for form
    try {
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
        
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
        
        $edit_server = null;
        if ($edit_server_id) {
            $edit_server = Capsule::table('dcim_servers')->where('id', $edit_server_id)->first();
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $racks = collect([]);
        $locations = collect([]);
        $edit_server = null;
    }
    
    // If editing a server, show the edit form
    if ($edit_server) {
        // Show edit form (existing code for single server edit)
        echo '<div class="dcim-container">';
        echo '<div class="dcim-layout">';
        
        // Use shared sidebar
        dcim_generate_sidebar($modulelink);
        
        // Main content
        echo '<div class="dcim-main">';
        echo '<div class="main-header">';
        echo '<button class="back-btn" onclick="goBack()">';
        echo '<i class="fas fa-arrow-left"></i>';
        echo '</button>';
        echo '<div class="main-title">Edit Server</div>';
        echo '</div>';
        
        echo '<div class="rack-visualization">';
        echo '<div style="flex: 1; padding: 0;">';
        
        // Edit form code here (keeping existing edit functionality)
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; padding: 32px; margin: 24px;">';
        echo '<h3 style="margin: 0 0 24px 0; color: #111827; font-weight: 600;">Edit Server Details</h3>';
        echo '<form method="post">';
        echo '<input type="hidden" name="action" value="update_server">';
        // ... rest of edit form fields ...
        echo '<button type="submit" class="add-location-btn" style="padding: 12px 24px;">';
        echo '<i class="fas fa-save"></i> Update Server';
        echo '</button>';
        echo '</form>';
        echo '</div>';
        
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    echo '<style>
    /* Modern Modal Interface CSS */

    /* Override any WHMCS defaults */
    .dcim-modal * {
        box-sizing: border-box !important;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    }

    /* Form controls styling to match the image */
    .dcim-modal .form-control {
        display: block !important;
        width: 100% !important;
        padding: 8px 12px !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        line-height: 1.5 !important;
        color: #495057 !important;
        background-color: #fff !important;
        background-clip: padding-box !important;
        border: 1px solid #d1d5db !important;
        border-radius: 6px !important;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    }

    .dcim-modal .form-control:focus {
        color: #495057 !important;
        background-color: #fff !important;
        border-color: #5b21b6 !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.2rem rgba(91, 33, 182, 0.25) !important;
    }

    .dcim-modal .form-control::placeholder {
        color: #9ca3af !important;
        opacity: 1 !important;
    }

    /* Select dropdown styling */
    .dcim-modal select.form-control {
        cursor: pointer !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\'%3e%3cpath fill=\'none\' stroke=\'%23343a40\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'2\' d=\'M2 5l6 6 6-6\'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right 0.75rem center !important;
        background-size: 16px 12px !important;
        padding-right: 2.25rem !important;
    }

    /* Button styling */
    .dcim-modal .btn {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-weight: 500 !important;
        text-align: center !important;
        white-space: nowrap !important;
        vertical-align: middle !important;
        user-select: none !important;
        border: 1px solid transparent !important;
        transition: all 0.15s ease-in-out !important;
        text-decoration: none !important;
    }

    .dcim-modal .btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    }

    .dcim-modal .btn:active {
        transform: translateY(0) !important;
    }

    /* Table styling to match the image */
    .dcim-modal .table {
        width: 100% !important;
        margin-bottom: 0 !important;
        color: #212529 !important;
        border-collapse: collapse !important;
    }

    .dcim-modal .table th {
        vertical-align: middle !important;
        border-top: none !important;
        border-bottom: 1px solid #e5e7eb !important;
        text-transform: uppercase !important;
        font-size: 12px !important;
        letter-spacing: 0.05em !important;
        font-weight: 500 !important;
        color: #6b7280 !important;
        background-color: #f9fafb !important;
    }

    .dcim-modal .table td {
        vertical-align: middle !important;
        border-top: none !important;
        border-bottom: 1px solid #f3f4f6 !important;
    }

    .dcim-modal .table tbody tr:hover {
        background-color: #f9fafb !important;
    }

    /* Icon buttons */
    .dcim-modal .btn-icon {
        width: 32px !important;
        height: 32px !important;
        padding: 0 !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Modal overlay fade effect */
    @keyframes modalFadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes modalSlideIn {
        from {
            transform: scale(0.95);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    .dcim-modal-overlay {
        animation: modalFadeIn 0.3s ease-out !important;
    }

    .dcim-modal-content {
        animation: modalSlideIn 0.3s ease-out !important;
    }

    /* Tab styling */
    .dcim-modal .nav-tabs {
        border-bottom: none !important;
        margin-bottom: 0 !important;
    }

    .dcim-modal .nav-tabs > li {
        margin-bottom: 0 !important;
    }

    .dcim-modal .nav-tabs > li > a {
        margin-right: 0 !important;
        line-height: 1.5 !important;
        border: none !important;
        border-radius: 0 !important;
        position: relative !important;
        padding: 16px 0 !important;
        margin-right: 32px !important;
        transition: all 0.3s ease !important;
    }

    .dcim-modal .nav-tabs > li > a:hover {
        border-color: transparent !important;
        background-color: transparent !important;
        color: #4f46e5 !important;
    }

    .dcim-modal .nav-tabs > li.active > a {
        color: #4f46e5 !important;
        background-color: transparent !important;
        border: none !important;
        border-bottom: 3px solid #4f46e5 !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .dcim-modal-content {
            margin: 10px !important;
            max-height: calc(100vh - 20px) !important;
        }
        
        .dcim-modal .table {
            font-size: 12px !important;
        }
        
        .dcim-modal .btn {
            padding: 8px 16px !important;
            font-size: 13px !important;
        }
    }

    /* Custom scrollbar for the modal */
    .dcim-modal-content::-webkit-scrollbar {
        width: 10px !important;
    }

    .dcim-modal-content::-webkit-scrollbar-track {
        background: #f1f5f9 !important;
    }

    .dcim-modal-content::-webkit-scrollbar-thumb {
        background: #cbd5e0 !important;
        border-radius: 5px !important;
    }

    .dcim-modal-content::-webkit-scrollbar-thumb:hover {
        background: #a0aec0 !important;
    }

    /* Purple theme colors matching your image */
    .btn-primary-purple {
        background-color: #5b21b6 !important;
        border-color: #5b21b6 !important;
        color: white !important;
    }

    .btn-primary-purple:hover {
        background-color: #4c1d95 !important;
        border-color: #4c1d95 !important;
    }

    .btn-outline-purple {
        color: #5b21b6 !important;
        border-color: #5b21b6 !important;
        background-color: transparent !important;
    }

    .btn-outline-purple:hover {
        color: white !important;
        background-color: #5b21b6 !important;
        border-color: #5b21b6 !important;
    }
    </style>';

    } else {
        // Show the new bulk addition modal interface
        if ($is_ajax) {
            // For AJAX requests, only return the modal content
            dcim_render_bulk_server_interface($modulelink, $racks, $locations, true);
            return; // Stop execution here for AJAX requests
        } else {
            // For regular requests, render the full page
            dcim_render_bulk_server_interface($modulelink, $racks, $locations, false);
        }
    }

    
}

/**
 * Get racks grouped by city for the location tab
 */
function dcim_get_racks_by_city() {
    try {
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.id', 'dcim_racks.name', 'dcim_racks.units', 'dcim_locations.city')
            ->get();
        
        $racksByCity = [];
        foreach ($racks as $rack) {
            if (!empty($rack->city)) {
                if (!isset($racksByCity[$rack->city])) {
                    $racksByCity[$rack->city] = [];
                }
                $racksByCity[$rack->city][] = [
                    'id' => $rack->id,
                    'name' => $rack->name,
                    'units' => $rack->units
                ];
            }
        }
        
        return $racksByCity;
    } catch (Exception $e) {
        // Return fallback data if database is not available
        return [
            'New York' => [
                ['id' => 1, 'name' => 'NYC-A01', 'units' => 42],
                ['id' => 2, 'name' => 'NYC-A02', 'units' => 42]
            ],
            'Los Angeles' => [
                ['id' => 3, 'name' => 'LAX-B01', 'units' => 42],
                ['id' => 4, 'name' => 'LAX-B02', 'units' => 42]
            ],
            'Chicago' => [
                ['id' => 5, 'name' => 'CHI-C01', 'units' => 42]
            ]
        ];
    }
}

/**
 * Render the bulk server addition interface
 */
function dcim_render_bulk_server_interface($modulelink, $racks, $locations, $is_ajax = false) {
    // Get CPU models and RAM configurations from database
    $cpu_models = dcim_get_cpu_models();
    $ram_configs = dcim_get_ram_configs();
    // Modern modal-style container
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 1200px; width: 100%; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Modal header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Bulk Add Dedicated Servers</h2>';
    echo '<button type="button" onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>';
    echo '</div>';
    
    // Tab navigation
    echo '<div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">';
    echo '<ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">';
    
    $tabs = [
        'basic-info' => ['icon' => 'fa-folder', 'label' => 'Basic Info'],
        'network' => ['icon' => 'fa-network-wired', 'label' => 'Network'],
        'location' => ['icon' => 'fa-globe', 'label' => 'Location'],
        'ipmi' => ['icon' => 'fa-server', 'label' => 'IPMI']
    ];
    
    foreach ($tabs as $key => $tab) {
        $isActive = ($key === 'basic-info') ? 'active' : '';
        echo '<li role="presentation" class="' . $isActive . '" style="margin: 0; list-style: none;">';
        echo '<a href="#' . $key . '" aria-controls="' . $key . '" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: ' . ($isActive ? '#4f46e5' : '#6b7280') . '; font-weight: 500; text-decoration: none; border-bottom: 3px solid ' . ($isActive ? '#4f46e5' : 'transparent') . '; margin-bottom: -1px;">';
        echo '<i class="fas ' . $tab['icon'] . '" style="font-size: 14px;"></i>';
        echo $tab['label'];
        echo '</a>';
        echo '</li>';
    }
    
    echo '</ul>';
    echo '</div>';
    
    // Form
    $form_onsubmit = $is_ajax ? 'onsubmit="console.log(\'Form submit intercepted\'); return handleAjaxSubmit(this, event)"' : '';
    echo '<form method="post" id="bulkServerForm" ' . $form_onsubmit . ' style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    echo '<input type="hidden" name="action" value="bulk_add_servers" id="actionField">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1" id="ajaxField">';
    }
    
    // Tab content
    echo '<div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">';
    
    // Basic Info Tab
    echo '<div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addBasicRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveBasicRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-th"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    echo '<div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">';
    echo '<div class="table-responsive">';
    echo '<table class="table" style="margin: 0;">';
    echo '<thead>';
    echo '<tr>';
    echo '<th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label*</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">CPU*</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">RAM*</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Status</th>';
    echo '<th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody id="basicInfoContainer">';
    // Pre-populate with 10 rows
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="servers[' . ($i-1) . '][label]" class="form-control device-label-input" placeholder="Device ' . $i . '" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="servers[' . ($i-1) . '][cpu_model]" class="form-control cpu-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select CPU Model</option>';
        foreach ($cpu_models as $cpu) {
            echo '<option value="' . htmlspecialchars($cpu->name) . '">' . htmlspecialchars($cpu->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddCpuModal()" title="Add CPU Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="servers[' . ($i-1) . '][ram_config]" class="form-control ram-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select RAM Configuration</option>';
        foreach ($ram_configs as $ram) {
            echo '<option value="' . htmlspecialchars($ram->name) . '">' . htmlspecialchars($ram->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddRamModal()" title="Add RAM Configuration" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="servers[' . ($i-1) . '][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="available">Available</option>';
        echo '<option value="provisioning">Provisioning</option>';
        echo '<option value="maintenance">Maintenance</option>';
        echo '<option value="offline">Offline</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 6px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeBasicRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Network Tab
    echo '<div role="tabpanel" class="tab-pane" id="network" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addNetworkRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveNetworkRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-th"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    echo '<div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">';
    echo '<div class="table-responsive">';
    echo '<table class="table" style="margin: 0;">';
    echo '<thead>';
    echo '<tr>';
    echo '<th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Switch</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Ports</th>';
    echo '<th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody id="networkContainer">';
    // Pre-populate with 10 rows
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;"><span id="device-name-network-' . $i . '" style="color: #111827; font-weight: 500;">Device ' . $i . '</span></td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="servers[' . ($i-1) . '][switch]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Switch</option>';
        echo '<option value="SW-CORE-01">SW-CORE-01 (Core Switch)</option>';
        echo '<option value="SW-CORE-02">SW-CORE-02 (Core Switch)</option>';
        echo '<option value="SW-TOR-A01">SW-TOR-A01 (Top of Rack)</option>';
        echo '<option value="SW-TOR-A02">SW-TOR-A02 (Top of Rack)</option>';
        echo '<option value="SW-TOR-B01">SW-TOR-B01 (Top of Rack)</option>';
        echo '<option value="SW-TOR-B02">SW-TOR-B02 (Top of Rack)</option>';
        echo '<option value="SW-MGMT-01">SW-MGMT-01 (Management)</option>';
        echo '<option value="SW-STORAGE-01">SW-STORAGE-01 (Storage)</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="servers[' . ($i-1) . '][ports]" class="form-control" placeholder="Select a switch first" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: #f9fafb;" readonly>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 6px;">';
        echo '<button type="button" class="btn btn-sm" onclick="autoAssignPorts(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>';
        echo '<button type="button" class="btn btn-sm" onclick="removeNetworkRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Location Tab
    echo '<div role="tabpanel" class="tab-pane" id="location" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addLocationRowFunc()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveLocationRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-th"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    echo '<div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">';
    echo '<div class="table-responsive">';
    echo '<table class="table" style="margin: 0;">';
    echo '<thead>';
    echo '<tr>';
    echo '<th style="width: 60px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">#</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Label</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">City*</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Rack*</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Top Position</th>';
    echo '<th style="padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Size U</th>';
    echo '<th style="width: 120px; padding: 12px; font-weight: 600; color: #6b7280; font-size: 13px; text-transform: uppercase; letter-spacing: 0.05em;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody id="locationContainer">';
    // Pre-populate with 10 rows
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;"><span id="device-name-location-' . $i . '" style="color: #111827; font-weight: 500;">Device ' . $i . '</span></td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="servers[' . ($i-1) . '][city]" class="form-control city-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;" onchange="updateRacksForCity(this)">';
        echo '<option value="">Select City</option>';
        // Populate cities from locations
        try {
            $unique_cities = Capsule::table('dcim_locations')->distinct()->pluck('city');
            foreach ($unique_cities as $city) {
                if (!empty($city)) {
                    echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
                }
            }
        } catch (Exception $e) {
            // Fallback cities if database is not available
            echo '<option value="New York">New York</option>';
            echo '<option value="Los Angeles">Los Angeles</option>';
            echo '<option value="Chicago">Chicago</option>';
            echo '<option value="Houston">Houston</option>';
            echo '<option value="Phoenix">Phoenix</option>';
            echo '<option value="Philadelphia">Philadelphia</option>';
            echo '<option value="San Antonio">San Antonio</option>';
            echo '<option value="San Diego">San Diego</option>';
            echo '<option value="Dallas">Dallas</option>';
            echo '<option value="San Jose">San Jose</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="servers[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; color: #9ca3af;" disabled>';
        echo '<option value="">Select a city first</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="number" name="servers[' . ($i-1) . '][start_unit]" class="form-control" placeholder="e.g. 10" min="1" max="42" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="number" name="servers[' . ($i-1) . '][unit_size]" class="form-control" placeholder="e.g. 2" min="1" max="8" value="1" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 6px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeLocationRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // IPMI Tab
    echo '<div role="tabpanel" class="tab-pane" id="ipmi" style="display: none; flex: 1; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveRows()" style="background: white; color: #5b21b6; border: 1px solid #5b21b6; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-th"></i> Add 5 Rows';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="detectMacAddresses()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px; margin-left: auto;">';
    echo '<i class="fas fa-wifi"></i> Detect MAC Addresses';
    echo '</button>';
    echo '</div>';
    
    // IPMI Table
    echo '<div style="padding: 20px 32px; overflow-y: auto; flex: 1; min-height: 0;">';
    echo '<div class="table-responsive">';
    echo '<table class="table" style="margin: 0; background: white;">';
    echo '<thead style="background: #f9fafb;">';
    echo '<tr>';
    echo '<th style="width: 60px; padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">IPMI Address</th>';
    echo '<th style="padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">IPMI Root Password</th>';
    echo '<th style="padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">MAC Address</th>';
    echo '<th style="width: 120px; padding: 16px; font-weight: 500; color: #6b7280; font-size: 13px; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody id="ipmiContainer">';
    // Pre-populate with 9 rows as shown in the image
    for ($i = 1; $i <= 9; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;"><span id="device-name-ipmi-' . $i . '" style="color: #111827; font-weight: 500;">Device ' . $i . '</span></td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="servers[' . ($i-1) . '][ipmi_address]" class="form-control" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; color: #6b7280;">';
        echo '<option value="" style="color: #9ca3af;">Select a city first</option>';
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" style="background: white; border: 1px solid #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer;"><i class="fas fa-sync-alt"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<input type="password" name="servers[' . ($i-1) . '][ipmi_password]" class="form-control" placeholder="Root Password" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<button type="button" class="btn btn-sm" onclick="togglePassword(this)" style="background: white; border: 1px solid #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer;"><i class="fas fa-eye"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="servers[' . ($i-1) . '][mac_address]" class="form-control" placeholder="MAC Address" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 6px;">';
        echo '<button type="button" class="btn btn-sm" onclick="copyRow(this)" title="Copy" style="background: white; border: 1px solid #d1d5db; color: #6b7280; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-copy"></i></button>';
        echo '<button type="button" class="btn btn-sm" onclick="removeRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>'; // End tab-content
    
    // Modal footer
    echo '<div style="padding: 20px 32px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">';
    echo '<span style="color: #6b7280; font-size: 14px;"><span id="serverCount">0</span> Dedicated Servers configured</span>';
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="button" class="btn" onclick="closeModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" class="btn" disabled style="background: #5b21b6; color: white; border: none; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px; opacity: 0.6;">';
    echo '<i class="fas fa-plus-circle"></i> Add Dedicated Servers';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '</form>';
    echo '</div>'; // End modal content
    echo '</div>'; // End modal container
    
    // Add CPU Model Modal
    echo '<div id="addCpuModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add CPU Model</h3>';
    echo '<button type="button" onclick="closeAddCpuModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addCpuForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">CPU Model Name *</label>';
    echo '<input type="text" id="cpuName" placeholder="e.g., Intel Xeon E5-2680v4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Manufacturer</label>';
    echo '<input type="text" id="cpuManufacturer" placeholder="e.g., Intel" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Cores</label>';
    echo '<input type="text" id="cpuCores" placeholder="e.g., 16" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Frequency</label>';
    echo '<input type="text" id="cpuFrequency" placeholder="e.g., 2.4GHz" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="cpuDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddCpuModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Add CPU Model</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    // Add RAM Configuration Modal
    echo '<div id="addRamModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add RAM Configuration</h3>';
    echo '<button type="button" onclick="closeAddRamModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addRamForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">RAM Configuration Name *</label>';
    echo '<input type="text" id="ramName" placeholder="e.g., 64GB DDR4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Size</label>';
    echo '<input type="text" id="ramSize" placeholder="e.g., 64GB" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Type</label>';
    echo '<input type="text" id="ramType" placeholder="e.g., DDR4" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Speed</label>';
    echo '<input type="text" id="ramSpeed" placeholder="e.g., 2666MHz" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="ramDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddRamModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer;">Add RAM Configuration</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    // Enhanced JavaScript
    echo '<script>
    // Global variables
    let currentTabIndex = 0;
    const maxServers = 10;
    const isAjaxModal = ' . ($is_ajax ? 'true' : 'false') . ';
    
    // Available racks by city (for demonstration)
    const racksByCity = ' . json_encode(dcim_get_racks_by_city()) . ';
    
    // Initialize server count and button state
    function updateServerCount() {
        // Count servers that have ALL required fields filled
        let count = 0;
        let validServers = [];
        
        console.log("updateServerCount called, checking all required fields");
        
        // Check each server row for completeness
        for (let i = 0; i < 10; i++) {
            // Get all required fields for this server
            const labelInput = document.querySelector("input[name=" + String.fromCharCode(34) + "servers[" + i + "][label]" + String.fromCharCode(34) + "]");
            const cpuSelect = document.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][cpu_model]" + String.fromCharCode(34) + "]");
            const ramSelect = document.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][ram_config]" + String.fromCharCode(34) + "]");
            const citySelect = document.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][city]" + String.fromCharCode(34) + "]");
            const rackSelect = document.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][rack_id]" + String.fromCharCode(34) + "]");
            
            if (!labelInput || !cpuSelect || !ramSelect || !citySelect || !rackSelect) {
                continue;
            }
            
            // Check if all required fields are filled
            const label = labelInput.value ? labelInput.value.trim() : "";
            const cpu = cpuSelect.value ? cpuSelect.value.trim() : "";
            const ram = ramSelect.value ? ramSelect.value.trim() : "";
            const city = citySelect.value ? citySelect.value.trim() : "";
            const rack = rackSelect.value ? rackSelect.value.trim() : "";
            
            // Validate label (must be user-entered, not placeholder)
            const hasValidLabel = label !== "" && !label.startsWith("Device ");
            const hasCpu = cpu !== "";
            const hasRam = ram !== "";
            const hasCity = city !== "";
            const hasRack = rack !== "";
            
            console.log("Server " + (i + 1) + ": Label=" + label + " (" + hasValidLabel + "), CPU=" + cpu + " (" + hasCpu + "), RAM=" + ram + " (" + hasRam + "), City=" + city + " (" + hasCity + "), Rack=" + rack + " (" + hasRack + ")");
            
            // Only count servers with ALL required fields filled
            if (hasValidLabel && hasCpu && hasRam && hasCity && hasRack) {
                count++;
                validServers.push(i + 1);
                console.log("Server " + (i + 1) + " is VALID - all required fields filled");
            } else {
                console.log("Server " + (i + 1) + " is INVALID - missing required fields");
            }
        }
        
        console.log("Total valid servers:", count, "Valid server numbers:", validServers);
        
        const serverCountEl = document.getElementById("serverCount");
        const submitCountEl = document.getElementById("submitCount");
        
        if (serverCountEl) {
            serverCountEl.textContent = count;
        }
        if (submitCountEl) {
            submitCountEl.textContent = count;
        }
        
        // Update button text and state
        const submitBtn = document.querySelector("button[type=submit]");
        if (submitBtn) {
            const serverText = count === 1 ? "Server" : "Servers";
            
            if (count === 0) {
                // Disable button when no valid servers
                submitBtn.disabled = true;
                submitBtn.style.opacity = "0.6";
                submitBtn.style.cursor = "not-allowed";
                submitBtn.innerHTML = "<i class=" + String.fromCharCode(34) + "fas fa-plus-circle" + String.fromCharCode(34) + "></i> Add Dedicated Servers";
                submitBtn.title = "Please fill in all required fields (LABEL*, CPU*, RAM*, CITY*, RACK*) for at least one server";
            } else {
                // Enable button when there are valid servers
                submitBtn.disabled = false;
                submitBtn.style.opacity = "1";
                submitBtn.style.cursor = "pointer";
                submitBtn.innerHTML = "<i class=" + String.fromCharCode(34) + "fas fa-plus-circle" + String.fromCharCode(34) + "></i> Add <span id=" + String.fromCharCode(34) + "submitCount" + String.fromCharCode(34) + ">" + count + "</span> Dedicated " + serverText;
                submitBtn.title = "Add " + count + " server(s) with all required fields to the database";
            }
        }
        
        console.log("Server count updated to:", count);
    }
    
    // Handle AJAX form submission
    function handleAjaxSubmit(form, event) {
        console.log("handleAjaxSubmit called, isAjaxModal:", isAjaxModal);
        console.log("Form element:", form);
        console.log("Form action:", form.action);
        console.log("Form method:", form.method);
        
        if (!isAjaxModal) {
            return true; // Allow normal submission
        }
        
        // Prevent normal form submission
        if (event) {
            event.preventDefault();
        }
        
        console.log("Preventing default submission, starting AJAX submission");
        
        // Validate form before submission - check ALL required fields
        let hasValidServer = false;
        let filledCount = 0;
        
        // Check each server row for all required fields
        for (let i = 0; i < 10; i++) {
            const labelInput = form.querySelector("input[name=" + String.fromCharCode(34) + "servers[" + i + "][label]" + String.fromCharCode(34) + "]");
            const cpuSelect = form.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][cpu_model]" + String.fromCharCode(34) + "]");
            const ramSelect = form.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][ram_config]" + String.fromCharCode(34) + "]");
            const citySelect = form.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][city]" + String.fromCharCode(34) + "]");
            const rackSelect = form.querySelector("select[name=" + String.fromCharCode(34) + "servers[" + i + "][rack_id]" + String.fromCharCode(34) + "]");
            
            if (!labelInput || !cpuSelect || !ramSelect || !citySelect || !rackSelect) {
                continue;
            }
            
            // Check if any fields are filled for this server
            const label = labelInput.value ? labelInput.value.trim() : "";
            const cpu = cpuSelect.value ? cpuSelect.value.trim() : "";
            const ram = ramSelect.value ? ramSelect.value.trim() : "";
            const city = citySelect.value ? citySelect.value.trim() : "";
            const rack = rackSelect.value ? rackSelect.value.trim() : "";
            
            // Check if this server has any data (indicating user intent to add it)
            const hasAnyData = (label !== "" && !label.startsWith("Device ")) || cpu !== "" || ram !== "" || city !== "" || rack !== "";
            
            if (hasAnyData) {
                // If user started filling this server, check that ALL required fields are complete
                const hasValidLabel = label !== "" && !label.startsWith("Device ");
                const hasCpu = cpu !== "";
                const hasRam = ram !== "";
                const hasCity = city !== "";
                const hasRack = rack !== "";
                
                if (hasValidLabel && hasCpu && hasRam && hasCity && hasRack) {
                    hasValidServer = true;
                    filledCount++;
                }
            }
        }
        
        if (!hasValidServer) {
            // Show error message
            const errorDiv = document.createElement("div");
            errorDiv.style.cssText = "position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100; font-weight: 500; max-width: 400px;";
            errorDiv.innerHTML = "<i class=" + String.fromCharCode(34) + "fas fa-exclamation-triangle" + String.fromCharCode(34) + " style=" + String.fromCharCode(34) + "margin-right: 8px;" + String.fromCharCode(34) + "></i>Please fill in all required fields (LABEL*, CPU*, RAM*, CITY*, RACK*) for at least one server.";
            document.body.appendChild(errorDiv);
            
            // Remove error message after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
            
            console.log("Form validation failed: Missing required fields");
            return false;
        }
        
        console.log("Form validation passed:", filledCount, "servers with valid names");
        
        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding Servers...";
        submitBtn.disabled = true;
        
        // Debug form elements first
        console.log("Checking form elements:");
        const actionField = form.querySelector("input[name=\\"action\\"]");
        const ajaxField = form.querySelector("input[name=\\"ajax\\"]");
        console.log("Action field found:", !!actionField, actionField ? actionField.value : "not found");
        console.log("Ajax field found:", !!ajaxField, ajaxField ? ajaxField.value : "not found");
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Ensure action field is included
        if (!formData.has("action")) {
            console.log("Action field missing from FormData, adding it manually");
            formData.append("action", "bulk_add_servers");
        } else {
            console.log("Action field found in FormData:", formData.get("action"));
        }
        
        // Debug: Log form data
        console.log("Form data being submitted:");
        const formEntries = [];
        const allEntries = [];
        formData.forEach(function(value, key) {
            allEntries.push(key + ": " + value);
            if (key.includes("label") || key.includes("cpu_model") || key.includes("ram_config") || key === "action") {
                console.log(key + ": " + value);
                formEntries.push(key + ": " + value);
            }
        });
        console.log("Total relevant form entries:", formEntries.length);
        console.log("All form entries count:", allEntries.length);
        
        // Debug: Log the URL being used
        const ajaxUrl = "' . $modulelink . '&action=servers&ajax=1";
        console.log("AJAX URL:", ajaxUrl);
        
        // Submit via AJAX to the correct endpoint
        fetch(ajaxUrl, {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            console.log("Form submission response:", html.substring(0, 500));
            
            // Check for different types of success responses
            const isSuccess = html.includes("added successfully") || 
                             html.includes("position: fixed; top: 20px; right: 20px; background: #10b981") ||
                             html.includes("server(s) added successfully") ||
                             html.includes("ajaxSuccessIndicator") ||
                             html.includes("SUCCESS:");
            
            if (isSuccess) {
                console.log("Success detected in response");
                // Replace modal content to show success message
                const modalContainer = document.getElementById("addServerModalContainer");
                modalContainer.innerHTML = html;
                
                // Close modal and reload after showing success
                setTimeout(function() {
                    closeModal();
                    window.location.reload();
                }, 2000);
            } else {
                console.log("No success message, checking if full HTML page returned");
                
                // If we get a full HTML page, there was likely an error or redirect
                if (html.includes("<!DOCTYPE html>")) {
                    console.error("Full HTML page returned, likely an error occurred");
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    alert("Error submitting form. The page was reloaded. Please try again.");
                } else {
                    console.log("Updating modal content with response");
                    // Replace modal content with the response (might have errors)
                    const modalContainer = document.getElementById("addServerModalContainer");
                    modalContainer.innerHTML = html;
                    
                    // Re-execute scripts in new content
                    const scripts = modalContainer.querySelectorAll("script");
                    scripts.forEach(script => {
                        try {
                            const newScript = document.createElement("script");
                            newScript.textContent = script.textContent;
                            document.head.appendChild(newScript);
                            document.head.removeChild(newScript);
                        } catch (e) {
                            console.error("Error executing script:", e);
                        }
                    });
                    
                    // Re-initialize modal after content update
                    if (typeof initializeAjaxModal === "function") {
                        initializeAjaxModal();
                    }
                }
            }
        })
        .catch(error => {
            console.error("Form submission error:", error);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            alert("Error submitting form. Please try again.");
        });
        
        return false;
    }
    
    // Close modal
    function closeModal() {
        if (isAjaxModal) {
            // We are in an AJAX modal, hide the modal container
            const modalContainer = document.getElementById("addServerModalContainer");
            if (modalContainer) {
                modalContainer.style.display = "none";
                modalContainer.innerHTML = "";
            }
        } else {
            // We are on a direct page, navigate back
        window.location.href = "' . $modulelink . '";
        }
    }
    
    // Show CPU Model Modal
    function showAddCpuModal() {
        console.log("showAddCpuModal called");
        const modal = document.getElementById("addCpuModal");
        if (modal) {
            modal.style.display = "flex";
            console.log("CPU modal shown");
        } else {
            console.error("addCpuModal not found");
        }
    }
    
    // Close CPU Model Modal
    function closeAddCpuModal() {
        document.getElementById("addCpuModal").style.display = "none";
        document.getElementById("addCpuForm").reset();
    }
    
    // Show RAM Configuration Modal
    function showAddRamModal() {
        document.getElementById("addRamModal").style.display = "flex";
    }
    
    // Close RAM Configuration Modal
    function closeAddRamModal() {
        document.getElementById("addRamModal").style.display = "none";
        document.getElementById("addRamForm").reset();
    }
    
    // Add CPU Model via AJAX
    function addCpuModel(formData) {
        fetch("' . $modulelink . '", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add to all CPU dropdowns
                document.querySelectorAll(".cpu-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddCpuModal();
                showToast("CPU model added successfully!", "success");
            } else {
                showToast("Error adding CPU model: " + data.error, "error");
            }
        })
        .catch(error => {
            showToast("Network error: " + error.message, "error");
        });
    }
    
    // Add RAM Configuration via AJAX
    function addRamConfig(formData) {
        fetch("' . $modulelink . '", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add to all RAM dropdowns
                document.querySelectorAll(".ram-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddRamModal();
                showToast("RAM configuration added successfully!", "success");
            } else {
                showToast("Error adding RAM configuration: " + data.error, "error");
            }
        })
        .catch(error => {
            showToast("Network error: " + error.message, "error");
        });
    }
    
    // Show toast notification
    function showToast(message, type) {
        const toast = document.createElement("div");
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === "success" ? "#10b981" : "#ef4444"};
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1200;
            font-size: 14px;
            font-weight: 500;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    // Synchronize device labels across all tabs
    function syncDeviceLabels() {
        console.log("syncDeviceLabels called");
        const basicInputs = document.querySelectorAll(".device-label-input");
        console.log("Found basic inputs:", basicInputs.length);
        
        basicInputs.forEach((input, index) => {
            // Use the actual input value if provided, otherwise use the placeholder
            const deviceName = input.value.trim() || input.placeholder || "Device " + (index + 1);
            const rowNumber = index + 1;
            
            console.log("Syncing device", rowNumber, "with name:", deviceName);
            
            // Update network tab
            const networkSpan = document.getElementById("device-name-network-" + rowNumber);
            if (networkSpan) {
                networkSpan.textContent = deviceName;
                console.log("Updated network tab for device", rowNumber);
            }
            
            // Update location tab
            const locationSpan = document.getElementById("device-name-location-" + rowNumber);
            if (locationSpan) {
                locationSpan.textContent = deviceName;
                console.log("Updated location tab for device", rowNumber);
            }
            
            // Update IPMI tab
            const ipmiSpan = document.getElementById("device-name-ipmi-" + rowNumber);
            if (ipmiSpan) {
                ipmiSpan.textContent = deviceName;
                console.log("Updated IPMI tab for device", rowNumber);
            }
        });
    }
    
    // Copy row functionality (duplicate a row with same data)
    function copyRow(button) {
        const row = button.closest("tr");
        const tbody = row.parentElement;
        const newRow = row.cloneNode(true);
        
        // Update row number
        const rowCount = tbody.children.length + 1;
        newRow.querySelector("td:first-child").textContent = rowCount;
        
        // Update input names and IDs
        const inputs = newRow.querySelectorAll("input, select");
        inputs.forEach(input => {
            if (input.name) {
                input.name = input.name.replace(/\[\d+\]/, "[" + (rowCount - 1) + "]");
            }
            if (input.id) {
                input.id = input.id.replace(/\d+$/, rowCount);
            }
        });
        
        // Update device name spans
        const deviceNameSpans = newRow.querySelectorAll("span[id^=device-name-]");
        deviceNameSpans.forEach(span => {
            const tabName = span.id.split("-")[2];
            span.id = "device-name-" + tabName + "-" + rowCount;
        });
        
        // Add the new row
        tbody.appendChild(newRow);
        
        // Update server count and sync labels
        updateServerCount();
        syncDeviceLabels();
        
        console.log("Row copied successfully");
    }
    
    // Update racks dropdown when city is selected
    function updateRacksForCity(citySelect) {
        const selectedCity = citySelect.value;
        const rackSelect = citySelect.closest("tr").querySelector(".rack-select");
        
        // Clear existing options
        rackSelect.innerHTML = "<option value=\"\">Select Rack</option>";
        
        if (selectedCity && racksByCity[selectedCity]) {
            rackSelect.disabled = false;
            rackSelect.style.color = "#374151";
            
            racksByCity[selectedCity].forEach(rack => {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.name + " (" + rack.units + "U)";
                rackSelect.appendChild(option);
            });
        } else {
            rackSelect.disabled = true;
            rackSelect.style.color = "#9ca3af";
        }
    }
    
    // Auto-assign ports for network switch
    function autoAssignPorts(button) {
        const row = button.closest("tr");
        const switchSelect = row.querySelector("select[name*=\"[switch]\"]");
        const portsInput = row.querySelector("input[name*=\"[ports]\"]");
        
        if (switchSelect.value) {
            // Generate port assignments based on switch type
            let ports = "";
            const rowIndex = Array.from(row.parentNode.children).indexOf(row);
            
            if (switchSelect.value.includes("TOR")) {
                ports = "Gi1/" + (rowIndex + 1) + ", Gi2/" + (rowIndex + 1);
            } else if (switchSelect.value.includes("CORE")) {
                ports = "Te1/0/" + (rowIndex + 1);
            } else {
                ports = "Fa0/" + (rowIndex + 1);
            }
            
            portsInput.value = ports;
            portsInput.style.background = "#ffffff";
            portsInput.removeAttribute("readonly");
        } else {
            alert("Please select a switch first");
        }
    }
    
    // Basic Info tab functions
    function removeBasicRow(button) {
        removeRowWithAnimation(button, "basicInfoContainer");
    }
    
    // Network tab functions
    function removeNetworkRow(button) {
        removeRowWithAnimation(button, "networkContainer");
    }
    
    // Location tab functions
    function removeLocationRow(button) {
        removeRowWithAnimation(button, "locationContainer");
    }
    
    // Basic Info tab functions
    function addBasicRow() {
        console.log("addBasicRow called");
        const container = document.getElementById("basicInfoContainer");
        if (!container) {
            console.error("basicInfoContainer not found");
            return;
        }
        const rowCount = container.children.length + 1;
        const newRow = createBasicInfoRow(rowCount);
        container.appendChild(newRow);
        updateServerCount();
        syncDeviceLabels();
        console.log("Basic row added successfully");
    }
    
    function addFiveBasicRows() {
        for (let i = 0; i < 5; i++) {
            addBasicRow();
        }
    }
    
    // Network tab functions
    function addNetworkRow() {
        const container = document.getElementById("networkContainer");
        const rowCount = container.children.length + 1;
        const newRow = createNetworkRow(rowCount);
        container.appendChild(newRow);
        updateServerCount();
        syncDeviceLabels();
    }
    
    function addFiveNetworkRows() {
        for (let i = 0; i < 5; i++) {
            addNetworkRow();
        }
    }
    
    // Location tab functions
    function addLocationRowFunc() {
        const container = document.getElementById("locationContainer");
        const rowCount = container.children.length + 1;
        const newRow = createLocationInfoRow(rowCount);
        container.appendChild(newRow);
        updateServerCount();
        syncDeviceLabels();
    }
    
    function addFiveLocationRows() {
        for (let i = 0; i < 5; i++) {
            addLocationRowFunc();
        }
    }
    
    // IPMI tab functions (existing)
    function addRow() {
        const container = document.getElementById("ipmiContainer");
        const rowCount = container.children.length + 1;
        const newRow = createIpmiRow(rowCount);
        container.appendChild(newRow);
        updateServerCount();
        syncDeviceLabels();
    }
    
    function addFiveRows() {
        for (let i = 0; i < 5; i++) {
            addRow();
        }
    }
    
    function createIpmiRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.borderBottom = "1px solid #f3f4f6";
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;"><span id="device-name-ipmi-${rowNumber}" style="color: #111827; font-weight: 500;">Device ${rowNumber}</span></td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <select name="servers[${rowNumber-1}][ipmi_address]" class="form-control" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; color: #6b7280;">
                        <option value="" style="color: #9ca3af;">Select a city first</option>
                    </select>
                    <button type="button" class="btn btn-sm" style="background: white; border: 1px solid #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer;"><i class="fas fa-sync-alt"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <input type="password" name="servers[${rowNumber-1}][ipmi_password]" class="form-control" placeholder="Root Password" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                    <button type="button" class="btn btn-sm" onclick="togglePassword(this)" style="background: white; border: 1px solid #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer;"><i class="fas fa-eye"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <input type="text" name="servers[${rowNumber-1}][mac_address]" class="form-control" placeholder="MAC Address" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 6px;">
                    <button type="button" class="btn btn-sm" onclick="removeRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;
        return row;
    }
    
    // Create basic info row with current CPU and RAM options
    function createBasicInfoRow(rowNumber) {
        const row = document.createElement("tr");
        row.style.borderBottom = "1px solid #f3f4f6";
        
        // Get current CPU options
        const cpuOptions = Array.from(document.querySelector(".cpu-select").options).map(option => 
            `<option value="${option.value}">${option.textContent}</option>`
        ).join("");
        
        // Get current RAM options  
        const ramOptions = Array.from(document.querySelector(".ram-select").options).map(option => 
            `<option value="${option.value}">${option.textContent}</option>`
        ).join("");
        
        row.innerHTML = `
            <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
            <td style="padding: 16px;">
                                 <input type="text" name="servers[${rowNumber-1}][label]" class="form-control device-label-input" placeholder="Device ${rowNumber}" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <select name="servers[${rowNumber-1}][cpu_model]" class="form-control cpu-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                        ${cpuOptions}
                    </select>
                    <button type="button" class="btn btn-sm" onclick="showAddCpuModal()" title="Add CPU Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <div style="display: flex; gap: 8px; align-items: center;">
                    <select name="servers[${rowNumber-1}][ram_config]" class="form-control ram-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">
                        ${ramOptions}
                    </select>
                    <button type="button" class="btn btn-sm" onclick="showAddRamModal()" title="Add RAM Configuration" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>
                </div>
            </td>
            <td style="padding: 16px;">
                <select name="servers[${rowNumber-1}][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                    <option value="available">Available</option>
                    <option value="provisioning">Provisioning</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="offline">Offline</option>
                </select>
            </td>
                         <td style="padding: 16px;">
                 <div style="display: flex; gap: 6px;">
                     <button type="button" class="btn btn-sm" onclick="removeBasicRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                 </div>
             </td>
                 `;
         return row;
     }
     
     // Create network row with current device name
     function createNetworkRow(rowNumber) {
         const row = document.createElement("tr");
         row.style.borderBottom = "1px solid #f3f4f6";
         
         row.innerHTML = `
             <td style="padding: 16px; color: #374151; font-weight: 500;">${rowNumber}</td>
             <td style="padding: 16px;"><span id="device-name-network-${rowNumber}" style="color: #111827; font-weight: 500;">Device ${rowNumber}</span></td>
             <td style="padding: 16px;">
                 <select name="servers[${rowNumber-1}][switch]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">
                     <option value="">Select Switch</option>
                     <option value="SW-CORE-01">SW-CORE-01 (Core Switch)</option>
                     <option value="SW-CORE-02">SW-CORE-02 (Core Switch)</option>
                     <option value="SW-TOR-A01">SW-TOR-A01 (Top of Rack)</option>
                     <option value="SW-TOR-A02">SW-TOR-A02 (Top of Rack)</option>
                     <option value="SW-TOR-B01">SW-TOR-B01 (Top of Rack)</option>
                     <option value="SW-TOR-B02">SW-TOR-B02 (Top of Rack)</option>
                     <option value="SW-MGMT-01">SW-MGMT-01 (Management)</option>
                     <option value="SW-STORAGE-01">SW-STORAGE-01 (Storage)</option>
                 </select>
             </td>
             <td style="padding: 16px;">
                 <input type="text" name="servers[${rowNumber-1}][ports]" class="form-control" placeholder="Select a switch first" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: #f9fafb;" readonly>
             </td>
             <td style="padding: 16px;">
                 <div style="display: flex; gap: 6px;">
                     <button type="button" class="btn btn-sm" onclick="autoAssignPorts(this)" title="Auto-assign" style="background: white; border: 1px solid #10b981; color: #10b981; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-magic"></i></button>
                     <button type="button" class="btn btn-sm" onclick="removeNetworkRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>
                 </div>
             </td>
         `;
         return row;
     }
     
     // Create location row with current device name
     function createLocationInfoRow(rowNumber) {
         const row = document.createElement("tr");
         row.style.borderBottom = "1px solid #f3f4f6";
         
         // Create the basic row structure first
         row.innerHTML = [
             "<td style=\"padding: 16px; color: #374151; font-weight: 500;\">" + rowNumber + "</td>",
             "<td style=\"padding: 16px;\"><span id=\"device-name-location-" + rowNumber + "\" style=\"color: #111827; font-weight: 500;\">Device " + rowNumber + "</span></td>",
             "<td style=\"padding: 16px;\">",
                 "<select name=\"servers[" + (rowNumber-1) + "][city]\" class=\"form-control city-select\" style=\"border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;\" onchange=\"updateRacksForCity(this)\">",
                     "<option value=\"\">Select City</option>",
                 "</select>",
             "</td>",
             "<td style=\"padding: 16px;\">",
                 "<select name=\"servers[" + (rowNumber-1) + "][rack_id]\" class=\"form-control rack-select\" style=\"border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; color: #9ca3af;\" disabled>",
                     "<option value=\"\">Select a city first</option>",
                 "</select>",
             "</td>",
             "<td style=\"padding: 16px;\">",
                 "<input type=\"number\" name=\"servers[" + (rowNumber-1) + "][start_unit]\" class=\"form-control\" placeholder=\"e.g. 10\" min=\"1\" max=\"42\" style=\"border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;\">",
             "</td>",
             "<td style=\"padding: 16px;\">",
                 "<input type=\"number\" name=\"servers[" + (rowNumber-1) + "][unit_size]\" class=\"form-control\" placeholder=\"e.g. 2\" min=\"1\" max=\"8\" value=\"1\" style=\"border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;\">",
             "</td>",
             "<td style=\"padding: 16px;\">",
                 "<div style=\"display: flex; gap: 6px;\">",
                     "<button type=\"button\" class=\"btn btn-sm\" onclick=\"removeLocationRow(this)\" title=\"Delete\" style=\"background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;\"><i class=\"fas fa-trash\"></i></button>",
                 "</div>",
             "</td>"
         ].join("");
         
         return row;
     }
     
     // Toggle password visibility
    function togglePassword(button) {
        const input = button.parentElement.querySelector("input[type=password], input[type=text]");
        const icon = button.querySelector("i");
        
        if (input.type === "password") {
            input.type = "text";
            icon.classList.remove("fa-eye");
            icon.classList.add("fa-eye-slash");
        } else {
            input.type = "password";
            icon.classList.remove("fa-eye-slash");
            icon.classList.add("fa-eye");
        }
    }
    
    // Remove row (IPMI tab)
    function removeRow(button) {
        removeRowWithAnimation(button, "ipmiContainer");
    }
    
    // Generic row removal with animation
    function removeRowWithAnimation(button, containerId) {
        const row = button.closest("tr");
        row.style.transition = "opacity 0.3s, transform 0.3s";
        row.style.opacity = "0";
        row.style.transform = "translateX(20px)";
        
        setTimeout(() => {
            row.remove();
            updateRowNumbers(containerId);
            updateServerCount();
            syncDeviceLabels();
        }, 300);
    }
    

    
    // Update row numbers after deletion
    function updateRowNumbers(containerId) {
        const rows = document.querySelectorAll("#" + containerId + " tr");
        rows.forEach((row, index) => {
            // Update row number
            row.querySelector("td:first-child").textContent = index + 1;
            
            // Update device name spans
            const deviceNameSpans = row.querySelectorAll("span[id^=device-name-]");
            deviceNameSpans.forEach(span => {
                const tabName = span.id.split("-")[2];
                span.id = "device-name-" + tabName + "-" + (index + 1);
                if (!span.closest("td").querySelector("input")) {
                    span.textContent = "Device " + (index + 1);
                }
            });
            
            // Update input names
            const inputs = row.querySelectorAll("input, select");
            inputs.forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/\[\d+\]/, "[" + index + "]");
                }
            });
        });
    }
    
    // Detect MAC addresses
    function detectMacAddresses() {
        const rows = document.querySelectorAll("#ipmiContainer tr");
        rows.forEach((row, index) => {
            const macInput = row.querySelector("input[name*=mac_address]");
            if (macInput && !macInput.value) {
                const mac = "00:1B:44:" + 
                    Math.floor(Math.random()*256).toString(16).padStart(2, "0") + ":" +
                    Math.floor(Math.random()*256).toString(16).padStart(2, "0") + ":" +
                    Math.floor(Math.random()*256).toString(16).padStart(2, "0");
                macInput.value = mac.toUpperCase();
            }
        });
    }
    
    // Handle tab switching - Fixed for dynamic content
    function initializeTabs() {
        document.querySelectorAll(".nav-tabs a").forEach(tab => {
            tab.addEventListener("click", function(e) {
                e.preventDefault();
                
                // Sync device labels before switching tabs
                syncDeviceLabels();
                
                // Remove active class from all tabs
                document.querySelectorAll(".nav-tabs li").forEach(li => li.classList.remove("active"));
                document.querySelectorAll(".tab-pane").forEach(pane => {
                    pane.style.display = "none";
                    pane.style.flexDirection = "column";
                    pane.style.overflow = "hidden";
                });
                
                // Add active class to clicked tab
                this.parentElement.classList.add("active");
                const targetId = this.getAttribute("href").substring(1);
                const targetPane = document.getElementById(targetId);
                    if (targetPane) {
                targetPane.style.display = "flex";
                targetPane.style.flexDirection = "column";
                targetPane.style.overflow = "hidden";
                    }
                
                // Update tab styling
                document.querySelectorAll(".nav-tabs a").forEach(a => {
                    a.style.color = "#6b7280";
                    a.style.borderBottomColor = "transparent";
                });
                this.style.color = "#4f46e5";
                this.style.borderBottomColor = "#4f46e5";
            });
        });
        }
        
    // Make all functions globally available for AJAX modals FIRST
    window.updateServerCount = updateServerCount;
    window.closeModal = closeModal;
    window.handleAjaxSubmit = handleAjaxSubmit;
    window.syncDeviceLabels = syncDeviceLabels;
    window.copyRow = copyRow;
    window.togglePassword = togglePassword;
    window.showAddCpuModal = showAddCpuModal;
    window.closeAddCpuModal = closeAddCpuModal;
    window.showAddRamModal = showAddRamModal;
    window.closeAddRamModal = closeAddRamModal;
    window.autoAssignPorts = autoAssignPorts;
    window.updateRacksForCity = updateRacksForCity;
    window.detectMacAddresses = detectMacAddresses;
    window.createBasicInfoRow = createBasicInfoRow;
    window.createNetworkRow = createNetworkRow;
    window.createLocationInfoRow = createLocationInfoRow;
    window.createIpmiRow = createIpmiRow;
    window.removeRowWithAnimation = removeRowWithAnimation;
    window.updateRowNumbers = updateRowNumbers;
    
    // Add row functions
    window.addBasicRow = addBasicRow;
    window.addFiveBasicRows = addFiveBasicRows;
    window.addNetworkRow = addNetworkRow;
    window.addFiveNetworkRows = addFiveNetworkRows;
    window.addLocationRowFunc = addLocationRowFunc;
    window.addFiveLocationRows = addFiveLocationRows;
    window.addRow = addRow;
    window.addFiveRows = addFiveRows;
    
    // Remove row functions
    window.removeBasicRow = removeBasicRow;
    window.removeNetworkRow = removeNetworkRow;
    window.removeLocationRow = removeLocationRow;
    window.removeRow = removeRow;
    
    // Also add event listeners specifically to all label inputs
    function attachLabelListeners() {
        console.log("attachLabelListeners called");
        const labelInputs = document.querySelectorAll(".device-label-input");
        console.log("Found label inputs:", labelInputs.length);
        
        labelInputs.forEach((input, index) => {
            console.log("Attaching listeners to input", index + 1);
            
            input.addEventListener("input", function() {
                console.log("Direct input event on label:", this.value);
                syncDeviceLabels();
                updateServerCount();
            });
            
            input.addEventListener("change", function() {
                console.log("Change event on label:", this.value);
                syncDeviceLabels();
                updateServerCount();
            });
            
            input.addEventListener("blur", function() {
                console.log("Blur event on label:", this.value);
                syncDeviceLabels();
                updateServerCount();
            });
        });
        
        console.log("Label listeners attached successfully");
    }
    
    // Initialize modal content
    function initializeModalContent() {
        console.log("Initializing modal content...");
        updateServerCount();
        initializeTabs();
        syncDeviceLabels();
        
        // Attach label listeners for dynamic content
        if (typeof attachLabelListeners === "function") {
            attachLabelListeners();
        }
        
        console.log("Modal content initialized");
    }
    
    // Make these globally available
    window.attachLabelListeners = attachLabelListeners;
    window.initializeModalContent = initializeModalContent;
    
    // Initialize on load
    document.addEventListener("DOMContentLoaded", function() {
        initializeModalContent();
        
        // Add backup form submission handler (removed due to scope issues - handled by onsubmit attribute)
        const bulkForm = document.getElementById("bulkServerForm");
        if (bulkForm) {
            console.log("Form found during DOMContentLoaded");
        } else {
            console.log("Form not found during DOMContentLoaded");
        }
        
        // Sync device labels when basic info labels change
        document.addEventListener("input", function(e) {
            if (e.target.classList.contains("device-label-input")) {
                console.log("Label input detected, syncing...");
                syncDeviceLabels();
                updateServerCount(); // Update count when labels change
            }
        });
        
        // Update server count when any required field changes
        document.addEventListener("change", function(e) {
            if (e.target.name && (
                e.target.name.includes("[cpu_model]") ||
                e.target.name.includes("[ram_config]") ||
                e.target.name.includes("[city]") ||
                e.target.name.includes("[rack_id]")
            )) {
                console.log("Required field changed:", e.target.name, "=", e.target.value);
                updateServerCount();
            }
        });
        
        // Attach listeners initially
        attachLabelListeners();
        
        // Handle switch changes for port assignment
        document.addEventListener("change", function(e) {
            if (e.target.name && e.target.name.includes("[switch]")) {
                const portsInput = e.target.closest("tr").querySelector("input[name*=\"[ports]\"]");
                if (e.target.value) {
                    portsInput.placeholder = "Click auto-assign or enter manually";
                    portsInput.style.background = "#ffffff";
                    portsInput.removeAttribute("readonly");
                } else {
                    portsInput.placeholder = "Select a switch first";
                    portsInput.style.background = "#f9fafb";
                    portsInput.setAttribute("readonly", true);
                    portsInput.value = "";
                }
            }
        });
        
        // Handle CPU form submission
        document.getElementById("addCpuForm").addEventListener("submit", function(e) {
            e.preventDefault();
            const formData = {
                ajax_action: "add_cpu_model",
                name: document.getElementById("cpuName").value,
                manufacturer: document.getElementById("cpuManufacturer").value,
                cores: document.getElementById("cpuCores").value,
                frequency: document.getElementById("cpuFrequency").value,
                description: document.getElementById("cpuDescription").value
            };
            addCpuModel(formData);
        });
        
        // Handle RAM form submission
        document.getElementById("addRamForm").addEventListener("submit", function(e) {
            e.preventDefault();
            const formData = {
                ajax_action: "add_ram_config",
                name: document.getElementById("ramName").value,
                size: document.getElementById("ramSize").value,
                type: document.getElementById("ramType").value,
                speed: document.getElementById("ramSpeed").value,
                description: document.getElementById("ramDescription").value
            };
            addRamConfig(formData);
        });
        
        // Close modals when clicking outside
        document.addEventListener("click", function(e) {
            if (e.target.id === "addCpuModal") {
                closeAddCpuModal();
            }
            if (e.target.id === "addRamModal") {
                closeAddRamModal();
            }
        });
    });
    </script>';
    
    // Additional CSS for better styling
    echo '<style>
    /* Remove WHMCS default styles that might interfere */
    .nav-tabs {
        border: none !important;
    }
    
    .nav-tabs > li {
        margin-bottom: 0 !important;
    }
    
    .nav-tabs > li > a {
        border: none !important;
        border-radius: 0 !important;
    }
    
    .form-control {
        box-shadow: none !important;
    }
    
    .form-control:focus {
        border-color: #5b21b6 !important;
        box-shadow: 0 0 0 3px rgba(91, 33, 182, 0.1) !important;
    }
    
    .btn:hover {
        transform: translateY(-1px);
        transition: all 0.2s;
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    ::-webkit-scrollbar-track {
        background: #f3f4f6;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
    }
    </style>';
}

/**
 * Render the bulk switch addition interface
 */
function dcim_render_bulk_switch_interface($modulelink, $racks, $locations, $is_ajax = false) {
    // Get switch models from database
    $switch_models = dcim_get_switch_models();
    
    // Modern modal-style container
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 1200px; width: 100%; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Modal header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Bulk Add Switches</h2>';
    echo '<button type="button" onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>';
    echo '</div>';
    
    // Tab navigation
    echo '<div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">';
    echo '<ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">';
    
    $tabs = [
        'basic-info' => ['icon' => 'fa-folder', 'label' => 'Basic Info'],
        'location' => ['icon' => 'fa-map-marker-alt', 'label' => 'Location'],
        'snmp-settings' => ['icon' => 'fa-cog', 'label' => 'SNMP Settings']
    ];
    
    foreach ($tabs as $key => $tab) {
        $isActive = ($key === 'basic-info') ? 'active' : '';
        echo '<li role="presentation" class="' . $isActive . '" style="margin: 0; list-style: none;">';
        echo '<a href="#' . $key . '" aria-controls="' . $key . '" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: ' . ($isActive ? '#4f46e5' : '#6b7280') . '; font-weight: 500; text-decoration: none; border-bottom: 3px solid ' . ($isActive ? '#4f46e5' : 'transparent') . '; margin-bottom: -1px;">';
        echo '<i class="fas ' . $tab['icon'] . '" style="font-size: 14px;"></i>';
        echo $tab['label'];
        echo '</a>';
        echo '</li>';
    }
    
    echo '</ul>';
    echo '</div>';
    
    // Form
    $form_onsubmit = $is_ajax ? 'onsubmit="console.log(\'Switch form submit intercepted\'); return handleSwitchAjaxSubmit(this, event)"' : '';
    echo '<form method="post" id="bulkSwitchForm" ' . $form_onsubmit . ' style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    echo '<input type="hidden" name="action" value="bulk_add_switches" id="actionField">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1" id="ajaxField">';
    }
    
    // Tab content
    echo '<div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">';
    
    // Basic Info Tab
    echo '<div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    // Table container with scroll
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; white-space: nowrap;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">Label*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">IP Address*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 150px;">Password*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 180px;">Model*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 120px;">Status</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 80px;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    // Generate initial rows
    for ($i = 1; $i <= 9; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][label]" class="form-control switch-label-input" placeholder="Switch Label" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="position: relative;">';
        echo '<select name="switches[' . ($i-1) . '][ip_address]" class="form-control switch-ip-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%; background: white;">';
        echo '<option value="">Select Switch IP address</option>';
        echo '</select>';
        echo '<div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280; pointer-events: none;"><i class="fas fa-refresh"></i></div>';
        echo '</div>';
        echo '<div style="color: #f59e0b; font-size: 12px; margin-top: 4px;">Select a city first</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="password" name="switches[' . ($i-1) . '][password]" class="form-control" placeholder="Password" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="switches[' . ($i-1) . '][model]" class="form-control switch-model-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select Switch Model</option>';
        foreach ($switch_models as $model) {
            echo '<option value="' . htmlspecialchars($model->name) . '">' . htmlspecialchars($model->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddSwitchModelModal()" title="Add Switch Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="available">Available</option>';
        echo '<option value="in_use">In Use</option>';
        echo '<option value="maintenance">Maintenance</option>';
        echo '<option value="offline">Offline</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
         // Location Tab
     echo '<div role="tabpanel" class="tab-pane" id="location" style="flex: 1; flex-direction: column; overflow: hidden; display: none;">';
    
    // Top action buttons for location
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">City*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Rack*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Top Position</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    // Group racks by city
    $racksByCity = [];
    foreach ($racks as $rack) {
        $city = $rack->location_name ?? 'Unknown';
        if (!isset($racksByCity[$city])) {
            $racksByCity[$city] = [];
        }
        $racksByCity[$city][] = $rack;
    }

    
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="switch-label-display">Device ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][city]" class="form-control city-select" onchange="updateSwitchRackOptions(' . ($i-1) . ')" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select City</option>';
        foreach ($racksByCity as $city => $cityRacks) {
            echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Rack</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][top_position]" class="form-control" placeholder="e.g. 38" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    // SNMP Settings Tab
    echo '<div role="tabpanel" class="tab-pane" id="snmp-settings" style="flex: 1; flex-direction: column; overflow: hidden; display: none;">';
    
    // Top action buttons for SNMP
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" class="btn" onclick="addSwitchRow()" style="background: #5b21b6; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus"></i> Add Row';
    echo '</button>';
    echo '<button type="button" class="btn" onclick="addFiveSwitchRows()" style="background: #6366f1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px;">';
    echo '<i class="fas fa-plus-circle"></i> Add 5 Rows';
    echo '</button>';
    echo '</div>';
    
    echo '<div style="flex: 1; overflow-y: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; background: white; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SNMP Community</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">SNMP Version</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="switch-label-display">Switch ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="position: relative;">';
        echo '<input type="text" name="switches[' . ($i-1) . '][snmp_community]" class="form-control" placeholder="Private Community String" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 32px 8px 12px; font-size: 14px; width: 100%;">';
        echo '<div style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280; pointer-events: none;"><i class="fas fa-lock"></i></div>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="switches[' . ($i-1) . '][snmp_version]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="2c">Version 2c</option>';
        echo '<option value="1">Version 1</option>';
        echo '<option value="3">Version 3</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeSwitchRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>'; // End tab-content
    
    // Modal footer
    echo '<div style="padding: 20px 32px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">';
    echo '<span style="color: #6b7280; font-size: 14px;"><span id="switchCount">0</span> Switches configured</span>';
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="button" class="btn" onclick="closeModal()" style="background: white; color: #374151; border: 1px solid #d1d5db; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" class="btn" disabled style="background: #5b21b6; color: white; border: none; padding: 10px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 8px; opacity: 0.6;">';
    echo '<i class="fas fa-plus-circle"></i> Add 0 Switches';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '</form>';
    echo '</div>'; // End modal content
         echo '</div>'; // End modal container
     
     // Add CSS for tab functionality
     echo '<style>
     .tab-pane {
         display: none !important;
         flex-direction: column;
         overflow: hidden;
     }
     .tab-pane.active {
         display: flex !important;
         flex-direction: column;
         overflow: hidden;
     }
     .nav-tabs {
         border-bottom: 1px solid #e5e7eb;
     }
     .nav-tabs li {
         margin-bottom: -1px;
         list-style: none;
     }
     
     /* Default tab link styling */
     .nav-tabs li a {
         color: #6b7280 !important;
         border-bottom: 3px solid transparent !important;
         transition: all 0.2s ease;
     }
     
     /* Active tab styling */
     .nav-tabs li.active a,
     .nav-tabs li.active a:hover,
     .nav-tabs li.active a:focus {
         color: #4f46e5 !important;
         border-bottom-color: #4f46e5 !important;
         background: none !important;
     }
     
     /* Hover effect for non-active tabs */
     .nav-tabs li:not(.active) a:hover {
         color: #374151 !important;
         border-bottom-color: #d1d5db !important;
     }
     
     /* Ensure table containers take full width and height */
     .tab-pane > div {
         flex: 1;
         width: 100%;
     }
     
     .tab-pane table {
         width: 100%;
         min-width: 100%;
     }
     </style>';
     
     // Enhanced JavaScript for switches
     echo '<script>
    // Global variables for switches
    let currentSwitchTabIndex = 0;
    const maxSwitches = 10;
    const isSwitchAjaxModal = ' . ($is_ajax ? 'true' : 'false') . ';
    
    // Available racks by city for switches
    const switchRacksByCity = ' . json_encode($racksByCity) . ';
    
    // Initialize switch count and button state
    function updateSwitchCount() {
        let count = 0;
        const switchLabelInputs = document.querySelectorAll(".switch-label-input");
        
        switchLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                count++;
            }
        });
        
        const countDisplay = document.getElementById("switchCount");
        if (countDisplay) {
            countDisplay.textContent = count;
        }
        
        // Update submit button
        const submitBtn = document.querySelector("button[type=submit]");
        if (submitBtn) {
            const switchText = count === 1 ? "Switch" : "Switches";
            
            if (count === 0) {
                submitBtn.disabled = true;
                submitBtn.style.opacity = "0.6";
                submitBtn.style.cursor = "not-allowed";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add 0 Switches";
                submitBtn.title = "Please fill in at least one switch label";
            } else {
                submitBtn.disabled = false;
                submitBtn.style.opacity = "1";
                submitBtn.style.cursor = "pointer";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add <span id=\"submitSwitchCount\">" + count + "</span> " + switchText;
                submitBtn.title = "Add " + count + " switch(es) to the database";
            }
        }
        
        console.log("Switch count updated to:", count);
    }
    
    // Handle AJAX form submission for switches
    function handleSwitchAjaxSubmit(form, event) {
        console.log("handleSwitchAjaxSubmit called, isSwitchAjaxModal:", isSwitchAjaxModal);
        
        if (!isSwitchAjaxModal) {
            return true; // Allow normal submission
        }
        
        // Prevent normal form submission
        if (event) {
            event.preventDefault();
        }
        
        console.log("Preventing default submission, starting switch AJAX submission");
        
        // Validate form before submission
        let hasValidSwitch = false;
        let filledCount = 0;
        
        // Check each switch row for at least a label
        for (let i = 0; i < 10; i++) {
            const labelInput = form.querySelector("input[name=\"switches[" + i + "][label]\"]");
            
            if (!labelInput) {
                continue;
            }
            
            const label = labelInput.value ? labelInput.value.trim() : "";
            
            if (label !== "") {
                hasValidSwitch = true;
                filledCount++;
            }
        }
        
        if (!hasValidSwitch) {
            const errorDiv = document.createElement("div");
            errorDiv.style.cssText = "position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100; font-weight: 500; max-width: 400px;";
            errorDiv.innerHTML = "<i class=\"fas fa-exclamation-triangle\" style=\"margin-right: 8px;\"></i>Please fill in at least one switch label.";
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
            
            console.log("Form validation failed: No switch labels provided");
            return false;
        }
        
        console.log("Switch form validation passed:", filledCount, "switches with valid labels");
        
        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding Switches...";
        submitBtn.disabled = true;
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Ensure action field is included
        if (!formData.has("action")) {
            formData.append("action", "bulk_add_switches");
        }
        
        console.log("Switch form data being submitted");
        
        // Submit via AJAX
        const ajaxUrl = "' . $modulelink . '&action=switches&ajax=1";
        console.log("Switch AJAX URL:", ajaxUrl);
        
        fetch(ajaxUrl, {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            console.log("Switch form submission response:", html.substring(0, 500));
            
            const isSuccess = html.includes("added successfully") || 
                             html.includes("position: fixed; top: 20px; right: 20px; background: #10b981") ||
                             html.includes("switch(es) added successfully") ||
                             html.includes("ajaxSuccessIndicator") ||
                             html.includes("SUCCESS:");
            
            if (isSuccess) {
                console.log("Switch success detected in response");
                const modalContainer = document.getElementById("addSwitchModalContainer");
                modalContainer.innerHTML = html;
                
                setTimeout(function() {
                    closeModal();
                    window.location.reload();
                }, 2000);
            } else {
                console.log("Switch submission completed but no clear success indicator");
                const modalContainer = document.getElementById("addSwitchModalContainer");
                if (modalContainer) {
                    modalContainer.innerHTML = html;
                    
                    const scripts = modalContainer.querySelectorAll("script");
                    scripts.forEach(script => {
                        try {
                            if (script.textContent) {
                                new Function(script.textContent)();
                            }
                        } catch (e) {
                            console.error("Error executing script:", e);
                        }
                    });
                    
                    if (typeof initializeSwitchAjaxModal === "function") {
                        initializeSwitchAjaxModal();
                    }
                }
            }
        })
        .catch(error => {
            console.error("Switch form submission error:", error);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            alert("Error submitting form. Please try again.");
        });
        
        return false;
    }
    
    // Switch-specific functions
    function addSwitchRow() {
        console.log("addSwitchRow called");
        // Implementation would add dynamic rows
        updateSwitchCount();
    }
    
    function addFiveSwitchRows() {
        for (let i = 0; i < 5; i++) {
            addSwitchRow();
        }
    }
    
    function removeSwitchRow(button) {
        // Implementation for removing switch rows
        console.log("removeSwitchRow called");
    }
    
    function updateSwitchRackOptions(switchIndex) {
        const citySelect = document.querySelector("select[name=\"switches[" + switchIndex + "][city]\"]");
        const rackSelect = document.querySelector("select[name=\"switches[" + switchIndex + "][rack_id]\"]");
        
        if (!citySelect || !rackSelect) return;
        
        const selectedCity = citySelect.value;
        rackSelect.innerHTML = "<option value=\"\">Select Rack</option>";
        
        if (selectedCity && switchRacksByCity[selectedCity]) {
            switchRacksByCity[selectedCity].forEach(function(rack) {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.name + " (" + rack.units + " units)";
                rackSelect.appendChild(option);
            });
        }
    }
    
    // Sync switch labels across tabs
    function syncSwitchLabels() {
        const labelInputs = document.querySelectorAll(".switch-label-input");
        const labelDisplays = document.querySelectorAll(".switch-label-display");
        
        labelInputs.forEach(function(input, index) {
            labelDisplays.forEach(function(display, displayIndex) {
                if (displayIndex === index) {
                    const label = input.value.trim() || (display.textContent.includes("Device") ? "Device " + (index + 1) : "Switch " + (index + 1));
                    display.textContent = label;
                }
            });
        });
    }
    

    
    // Initialize switch modal
    document.addEventListener("DOMContentLoaded", function() {
        // Update switch count when label inputs change
        document.addEventListener("input", function(e) {
            if (e.target.classList.contains("switch-label-input")) {
                console.log("Switch label input detected, syncing...");
                syncSwitchLabels();
                updateSwitchCount();
            }
        });
        
                 // Initialize tabs
         const tabLinks = document.querySelectorAll("[data-toggle=\"tab\"]");
         console.log("Found tab links:", tabLinks.length);
         
         tabLinks.forEach(function(link) {
             link.addEventListener("click", function(e) {
                 e.preventDefault();
                 const targetId = this.getAttribute("href").substring(1);
                 
                 console.log("Tab clicked:", targetId);
                 console.log("Clicked element:", this);
                 console.log("Parent element:", this.parentNode);
                 
                 // Remove active class from all tabs and panes
                 document.querySelectorAll(".nav-tabs li").forEach(function(li) {
                     li.classList.remove("active");
                     console.log("Removed active from:", li);
                 });
                 document.querySelectorAll(".tab-pane").forEach(function(pane) {
                     pane.classList.remove("active");
                     pane.style.display = "none";
                 });
                 
                 // Reset all tab link styles
                 document.querySelectorAll("[data-toggle=\"tab\"]").forEach(function(tabLink) {
                     tabLink.style.color = "#6b7280";
                     tabLink.style.borderBottomColor = "transparent";
                     // Also remove active class from parent li
                     if (tabLink.parentNode) {
                         tabLink.parentNode.classList.remove("active");
                     }
                 });
                 
                 // Add active class to clicked tab and target pane
                 this.parentNode.classList.add("active");
                 this.style.color = "#4f46e5 !important";
                 this.style.borderBottomColor = "#4f46e5 !important";
                 console.log("Added active to:", this.parentNode);
                 
                 const targetPane = document.getElementById(targetId);
                 if (targetPane) {
                     targetPane.classList.add("active");
                     targetPane.style.display = "flex";
                     targetPane.style.flexDirection = "column";
                     targetPane.style.overflow = "hidden";
                     console.log("Activated tab pane:", targetId);
                     
                     // Force a repaint to ensure proper display
                     setTimeout(function() {
                         const table = targetPane.querySelector("table");
                         if (table) {
                             table.style.width = "100%";
                             console.log("Table width set for tab:", targetId);
                         }
                     }, 50);
                 } else {
                     console.error("Target pane not found:", targetId);
                 }
             });
         });
        
                 // Set initial tab state properly
         console.log("Setting initial tab state...");
         
         // First, hide all tabs
         document.querySelectorAll(".tab-pane").forEach(function(pane) {
             pane.classList.remove("active");
             pane.style.display = "none";
         });
         
         // Remove active from all tab links
         document.querySelectorAll(".nav-tabs li").forEach(function(li) {
             li.classList.remove("active");
         });
         document.querySelectorAll("[data-toggle=\"tab\"]").forEach(function(tabLink) {
             tabLink.style.color = "#6b7280";
             tabLink.style.borderBottomColor = "transparent";
         });
         
         // Set basic-info as active
         const initialTab = document.getElementById("basic-info");
         const initialTabLink = document.querySelector("[href=\"#basic-info\"]");
         
         if (initialTab && initialTabLink) {
             initialTab.classList.add("active");
             initialTab.style.display = "flex";
             initialTab.style.flexDirection = "column";
             initialTab.style.overflow = "hidden";
             
             initialTabLink.parentNode.classList.add("active");
             initialTabLink.style.color = "#4f46e5";
             initialTabLink.style.borderBottomColor = "#4f46e5";
             
             console.log("Initial tab set to basic-info");
         } else {
             console.error("Could not find initial tab elements");
         }
         
         // Initial count update
         updateSwitchCount();
         syncSwitchLabels();
    });

    // Switch Model Modal Functions
    window.showAddSwitchModelModal = function showAddSwitchModelModal() {
        const modalHtml = `
            <div id="addSwitchModelModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1060; display: flex; align-items: center; justify-content: center; padding: 20px;">
                <div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 500px; width: 100%; max-height: 90vh; overflow: hidden;">
                    <div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">
                        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add Switch Model</h3>
                        <button type="button" onclick="closeAddSwitchModelModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px;">&times;</button>
                    </div>
                    <form id="addSwitchModelForm" style="padding: 24px 32px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Model Name *</label>
                            <input type="text" id="switchModelName" name="name" required placeholder="e.g., Cisco Catalyst 2960X" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Manufacturer</label>
                            <input type="text" id="switchModelManufacturer" name="manufacturer" placeholder="e.g., Cisco, HP, Juniper" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                        </div>
                        <div style="margin-bottom: 24px;">
                            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #374151;">Description</label>
                            <textarea id="switchModelDescription" name="description" rows="3" placeholder="Optional description or notes" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"></textarea>
                        </div>
                        <div style="display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" onclick="closeAddSwitchModelModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; cursor: pointer; font-size: 14px;">Cancel</button>
                            <button type="submit" style="padding: 8px 16px; border: none; border-radius: 6px; background: #5b21b6; color: white; cursor: pointer; font-size: 14px;">Add Model</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML("beforeend", modalHtml);
        
        // Handle form submission
        document.getElementById("addSwitchModelForm").addEventListener("submit", function(e) {
            e.preventDefault();
                         const formData = {
                 ajax_action: "add_switch_model",
                 name: document.getElementById("switchModelName").value,
                 manufacturer: document.getElementById("switchModelManufacturer").value,
                 description: document.getElementById("switchModelDescription").value
             };
            addSwitchModel(formData);
        });
    }

    window.closeAddSwitchModelModal = function closeAddSwitchModelModal() {
        const modal = document.getElementById("addSwitchModelModal");
        if (modal) {
            modal.remove();
        }
    }

    window.addSwitchModel = function addSwitchModel(formData) {
        fetch("' . $modulelink . '", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add to all switch model dropdowns
                document.querySelectorAll(".switch-model-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddSwitchModelModal();
                showToast("Switch model added successfully!", "success");
            } else {
                showToast("Error adding switch model: " + data.error, "error");
            }
        })
        .catch(error => {
            showToast("Network error: " + error.message, "error");
        });
    }


    
    </script>';
}

/**
 * Modern Dashboard
 */
function dcim_modern_dashboard($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Get all locations from database with error handling
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching locations - " . $e->getMessage());
        $locations = collect([]);
    }
    
    $selected_location = $_GET['location_id'] ?? null;
    
    // Only auto-redirect to rack view if both location_id AND rack_id are explicitly provided
    if ($selected_location && isset($_GET['rack_id'])) {
        return dcim_modern_rack_view($modulelink);
    }
    
    // Output modern interface with proper structure
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $selected_location);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">Data Center Infrastructure Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    
    if (count($locations) > 0) {
        if ($selected_location) {
            // Show racks for selected location
            try {
                $racks = Capsule::table('dcim_racks')->where('location_id', $selected_location)->get();
                $selected_location_name = Capsule::table('dcim_locations')->where('id', $selected_location)->value('name');
            } catch (Exception $e) {
                error_log("DCIM: Error fetching location data - " . $e->getMessage());
                $racks = collect([]);
                $selected_location_name = "Unknown Location";
            }
            
            echo '<div class="page-section-header">';
            echo '<h3>Racks in ' . htmlspecialchars($selected_location_name) . '</h3>';
            echo '</div>';
            
            if (count($racks) > 0) {
                echo '<div class="racks-grid">';
                foreach ($racks as $rack) {
                    $server_count = 0;
                    $used_units = 0;
                    
                    try {
                        $server_count = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->count();
                        $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->sum('unit_size');
                    } catch (Exception $e) {
                        error_log("DCIM: Error counting servers - " . $e->getMessage());
                    }
                    
                    echo '<div class="rack-card" onclick="selectRack(' . $rack->id . ')">';
                    echo '<div class="rack-card-header">';
                    echo '<div class="rack-name">' . htmlspecialchars($rack->name) . '</div>';
                    echo '<div class="rack-type">' . $rack->units . 'U Rack</div>';
                    echo '</div>';
                    echo '<div class="rack-stats">';
                    echo 'Servers: ' . $server_count . '<br>';
                    echo 'Used: ' . $used_units . '/' . $rack->units . 'U';
                    echo '</div>';
                    echo '</div>';
                }
                echo '</div>';
            } else {
                echo '<div class="empty-state">';
                echo '<i class="fas fa-server"></i>';
                echo '<h3>No Racks Found</h3>';
                echo '<p>No racks found in this location</p>';
                echo '<button class="add-location-btn" onclick="addRack(' . $selected_location . ')" style="margin-top: 16px;">';
                echo '<i class="fas fa-plus"></i> Add First Rack';
                echo '</button>';
                echo '</div>';
            }
        } else {
            echo '<div class="empty-state">';
            echo '<i class="fas fa-map-marker-alt"></i>';
            echo '<h3>Select a Location</h3>';
            echo '<p>Choose a data center location from the sidebar to view its racks and servers</p>';
            
            // Show infrastructure statistics
            try {
                $total_locations = count($locations);
                $total_racks = Capsule::table('dcim_racks')->count();
                $total_servers = Capsule::table('dcim_servers')->count();
                
                if ($total_locations > 0 || $total_racks > 0 || $total_servers > 0) {
                    echo '<div class="stats-panel" style="margin-top: 32px; display: inline-block;">';
                    echo '<div style="font-size: 14px; color: #4a5568; margin-bottom: 16px; font-weight: 600;">Your Infrastructure</div>';
                    echo '<div class="stats-grid">';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_locations . '</div>';
                    echo '<div class="stat-label">Locations</div>';
                    echo '</div>';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_racks . '</div>';
                    echo '<div class="stat-label">Racks</div>';
                    echo '</div>';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_servers . '</div>';
                    echo '<div class="stat-label">Servers</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                }
            } catch (Exception $e) {
                error_log("DCIM: Error fetching statistics - " . $e->getMessage());
            }
            echo '</div>';
        }
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-plus-circle"></i>';
        echo '<h3>Welcome to DCIM</h3>';
        echo '<p>Start by adding your first data center location, or load sample data for testing</p>';
        echo '<div style="margin-top: 20px; display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">';
        echo '<button class="add-location-btn" onclick="showAddLocationModal()">';
        echo '<i class="fas fa-plus"></i> Add First Location';
        echo '</button>';
        echo '<button class="add-location-btn" onclick="createSampleData()" style="background: #48bb78;">';
        echo '<i class="fas fa-database"></i> Load Sample Data';
        echo '</button>';
        echo '</div>';
        echo '<p style="font-size: 11px; color: #a0aec0; margin-top: 16px;">Sample data includes 2 locations, 4 racks, and demo servers</p>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function selectRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function createSampleData() {
        if (confirm("This will create sample locations, racks, and servers for testing purposes. Continue?")) {
            window.location.href = "' . $modulelink . '&create_sample=true";
        }
    }
    </script>';
}

/**
 * Servers table view
 */
function dcim_servers_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        $servers = Capsule::table('dcim_servers')
            ->leftJoin('dcim_racks', 'dcim_servers.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_servers.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_servers.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching servers: ' . $e->getMessage() . '</div>';
        $servers = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Servers</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="window.location.href=\'' . $modulelink . '&action=servers_manage\'" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Server</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Servers table
    if (count($servers) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Server</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Hardware</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">IP Address</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($servers as $server) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($server->name) . '</div>';
            if ($server->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($server->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->location_name ? htmlspecialchars($server->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->rack_name ? htmlspecialchars($server->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($server->start_unit) {
                echo 'U' . $server->start_unit;
                if ($server->unit_size > 1) {
                    echo '-' . ($server->start_unit + $server->unit_size - 1);
                }
                echo ' (' . $server->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($server->make || $server->model) {
                echo htmlspecialchars($server->make . ' ' . $server->model);
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($server->ip_address ? htmlspecialchars($server->ip_address) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$server->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($server->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editServer(' . $server->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($server->rack_id) {
                echo '<button onclick="viewInRack(' . $server->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-server"></i>';
        echo '<h3>No Servers Found</h3>';
        echo '<p>Add your first server to get started</p>';
        echo '<button onclick="showAddServerModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Server';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Server functionality
    echo '<div id="addServerModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function editServer(serverId) {
        window.location.href = "' . $modulelink . '&action=servers&edit=" + serverId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddServerModal() {
        // Show loading state
        const modalContainer = document.getElementById("addServerModalContainer");
        modalContainer.innerHTML = "<div style=\\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\\"><div style=\\"background: white; border-radius: 12px; padding: 40px; text-align: center;\\"><i class=\\"fas fa-spinner fa-spin\\" style=\\"font-size: 24px; color: #4299e1; margin-bottom: 16px;\\"></i><p style=\\"margin: 0; color: #6b7280;\\">Loading server configuration...</p></div></div>";
        modalContainer.style.display = "block";
        
        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=servers&ajax=1")
            .then(response => response.text())
            .then(html => {
                modalContainer.innerHTML = html;
                
                // Execute any scripts in the loaded content
                const scripts = modalContainer.querySelectorAll("script");
                scripts.forEach(script => {
                    try {
                        if (script.src) {
                            // For external scripts, load them
                            const newScript = document.createElement("script");
                            newScript.src = script.src;
                            newScript.onload = function() {
                                // Script loaded successfully
                            };
                            document.head.appendChild(newScript);
                        } else {
                            // For inline scripts, execute them
                            const scriptContent = script.textContent || script.innerText;
                            new Function(scriptContent)();
                        }
                    } catch (error) {
                        console.error("Error executing script:", error);
                    }
                });
                
                // Initialize modal content after scripts are loaded
                setTimeout(function() {
                    try {
                        // Force initialization of all modal functions
                        initializeAjaxModal();
                    } catch (error) {
                        console.log("Could not initialize modal content:", error);
                    }
                }, 200);
            })
            .catch(error => {
                console.error("Error loading modal:", error);
                modalContainer.innerHTML = "<div style=\\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\\"><div style=\\"background: white; border-radius: 12px; padding: 40px; text-align: center;\\"><i class=\\"fas fa-exclamation-triangle\\" style=\\"font-size: 24px; color: #ef4444; margin-bottom: 16px;\\"></i><p style=\\"margin: 0; color: #374151;\\">Error loading server configuration</p><button onclick=\\"closeModal()\\" style=\\"margin-top: 16px; background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;\\">Close</button></div></div>";
            });
    }
    
    function closeModal() {
        const modalContainer = document.getElementById("addServerModalContainer");
        modalContainer.style.display = "none";
        modalContainer.innerHTML = "";
    }
    
    // Initialize AJAX modal functionality
    function initializeAjaxModal() {
        console.log("initializeAjaxModal called");
        
        // Make sure all the global functions are available
        window.closeModal = function() {
            const modalContainer = document.getElementById("addServerModalContainer");
            if (modalContainer) {
                modalContainer.style.display = "none";
                modalContainer.innerHTML = "";
            }
        };
        
        // Form submission is handled by the onsubmit attribute - no additional listeners needed
        const bulkForm = document.getElementById("bulkServerForm");
        if (bulkForm) {
            console.log("AJAX modal form found - submission handled by onsubmit attribute");
        } else {
            console.log("AJAX modal form not found");
        }
        
                 // Initialize tabs functionality
         function initTabs() {
             const tabs = document.querySelectorAll(".nav-tabs a");
             console.log("Initializing tabs, found:", tabs.length);
             
             tabs.forEach(function(tab) {
                 tab.addEventListener("click", function(e) {
                     e.preventDefault();
                     console.log("Tab clicked:", this.textContent);
                     
                     // Remove active class from all tabs
                     document.querySelectorAll(".nav-tabs li").forEach(function(li) {
                         li.classList.remove("active");
                     });
                     document.querySelectorAll(".tab-pane").forEach(function(pane) {
                         pane.style.display = "none";
                     });
                     
                     // Add active class to clicked tab
                     this.parentElement.classList.add("active");
                     const targetId = this.getAttribute("href").substring(1);
                     const targetPane = document.getElementById(targetId);
                     console.log("Switching to tab:", targetId, "found:", !!targetPane);
                     
                     if (targetPane) {
                         targetPane.style.display = "flex";
                         targetPane.style.flexDirection = "column";
                         targetPane.style.overflow = "hidden";
                     }
                     
                     // Update tab styling
                     document.querySelectorAll(".nav-tabs a").forEach(function(a) {
                         a.style.color = "#6b7280";
                         a.style.borderBottomColor = "transparent";
                     });
                     this.style.color = "#4f46e5";
                     this.style.borderBottomColor = "#4f46e5";
                 });
             });
         }
        
        // Initialize tabs first
        initTabs();
        
        // Initialize all modal-specific functionality
        if (typeof window.initializeModalContent === "function") {
            window.initializeModalContent();
        } else {
            // Fallback initialization
            if (typeof window.updateServerCount === "function") {
                window.updateServerCount();
            }
            
            if (typeof window.syncDeviceLabels === "function") {
                window.syncDeviceLabels();
            }
            
            if (typeof window.attachLabelListeners === "function") {
                window.attachLabelListeners();
            }
        }
        
        // Add some debugging
        console.log("AJAX modal initialized successfully");
        console.log("Available functions:", {
            addBasicRow: typeof window.addBasicRow,
            addRow: typeof window.addRow,
            showAddCpuModal: typeof window.showAddCpuModal,
            togglePassword: typeof window.togglePassword,
            handleAjaxSubmit: typeof window.handleAjaxSubmit,
            copyRow: typeof window.copyRow,
            syncDeviceLabels: typeof window.syncDeviceLabels,
            attachLabelListeners: typeof window.attachLabelListeners
        });
    }

    </script>';
}

/**
 * Switches table view
 */
function dcim_switches_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        $switches = Capsule::table('dcim_switches')
            ->leftJoin('dcim_racks', 'dcim_switches.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_switches.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_switches.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching switches: ' . $e->getMessage() . '</div>';
        $switches = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Switches</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="window.location.href=\'' . $modulelink . '&action=switches_manage\'" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Switch</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Switches table
    if (count($switches) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Switch</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Type</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Ports</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Management IP</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($switches as $switch) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($switch->name) . '</div>';
            if ($switch->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($switch->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->location_name ? htmlspecialchars($switch->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->rack_name ? htmlspecialchars($switch->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($switch->start_unit) {
                echo 'U' . $switch->start_unit;
                if ($switch->unit_size > 1) {
                    echo '-' . ($switch->start_unit + $switch->unit_size - 1);
                }
                echo ' (' . $switch->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->switch_type ? htmlspecialchars(ucfirst($switch->switch_type)) : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->ports ? $switch->ports : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($switch->management_ip ? htmlspecialchars($switch->management_ip) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$switch->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($switch->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editSwitch(' . $switch->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($switch->rack_id) {
                echo '<button onclick="viewInRack(' . $switch->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-network-wired"></i>';
        echo '<h3>No Switches Found</h3>';
        echo '<p>Add your first switch to get started</p>';
        echo '<button onclick="showAddSwitchModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Switch';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Switch functionality
    echo '<div id="addSwitchModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function editSwitch(switchId) {
        window.location.href = "' . $modulelink . '&action=switches&edit=" + switchId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddSwitchModal() {
        // Show loading state
        const modalContainer = document.getElementById("addSwitchModalContainer");
        modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"font-size: 24px; color: #4299e1; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #6b7280;\">Loading switch configuration...</p></div></div>";
        modalContainer.style.display = "block";
        
        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=switches&ajax=1")
            .then(response => response.text())
            .then(html => {
                modalContainer.innerHTML = html;
                
                // Execute any scripts in the loaded content
                const scripts = modalContainer.querySelectorAll("script");
                scripts.forEach(script => {
                    try {
                        if (script.src) {
                            // For external scripts, load them
                            const newScript = document.createElement("script");
                            newScript.src = script.src;
                            newScript.onload = function() {
                                // Script loaded successfully
                            };
                            document.head.appendChild(newScript);
                        } else {
                            // For inline scripts, execute them
                            const scriptContent = script.textContent || script.innerText;
                            new Function(scriptContent)();
                        }
                    } catch (error) {
                        console.error("Error executing script:", error);
                    }
                });
                
                // Initialize modal content after scripts are loaded
                setTimeout(function() {
                    try {
                        // Force initialization of all modal functions
                        initializeSwitchAjaxModal();
                    } catch (error) {
                        console.log("Could not initialize switch modal content:", error);
                    }
                }, 200);
            })
            .catch(error => {
                console.error("Error loading switch modal:", error);
                modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-exclamation-triangle\" style=\"font-size: 24px; color: #ef4444; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #374151;\">Error loading switch configuration</p><button onclick=\"closeModal()\" style=\"margin-top: 16px; background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;\">Close</button></div></div>";
            });
    }
    
    function closeModal() {
        const modalContainer = document.getElementById("addSwitchModalContainer");
        modalContainer.style.display = "none";
        modalContainer.innerHTML = "";
    }
    
    // Initialize Switch AJAX modal functionality
    function initializeSwitchAjaxModal() {
        console.log("initializeSwitchAjaxModal called");
        
        // Make sure all the global functions are available
        window.closeModal = function() {
            const modalContainer = document.getElementById("addSwitchModalContainer");
            if (modalContainer) {
                modalContainer.style.display = "none";
                modalContainer.innerHTML = "";
            }
        };
        
        // Form submission is handled by the onsubmit attribute - no additional listeners needed
        const bulkSwitchForm = document.getElementById("bulkSwitchForm");
        if (bulkSwitchForm) {
            console.log("Switch AJAX modal form found - submission handled by onsubmit attribute");
        } else {
            console.log("Switch AJAX modal form not found");
        }
    }
    </script>';
}

/**
 * Chassies table view
 */
function dcim_chassies_table($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        $chassies = Capsule::table('dcim_chassies')
            ->leftJoin('dcim_racks', 'dcim_chassies.rack_id', '=', 'dcim_racks.id')
            ->leftJoin('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_chassies.*', 'dcim_racks.name as rack_name', 'dcim_locations.name as location_name')
            ->orderBy('dcim_chassies.name')
            ->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching chassies: ' . $e->getMessage() . '</div>';
        $chassies = collect([]);
    }
    
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">All Chassies</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button onclick="window.location.href=\'' . $modulelink . '&action=chassies_manage\'" class="add-location-btn" style="border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Chassis</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-visualization">';
    echo '<div style="flex: 1; padding: 32px;">';
    
    // Chassies table
    if (count($chassies) > 0) {
        echo '<div style="background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; margin-bottom: 24px;">';
        echo '<div style="overflow-x: auto;">';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<thead style="background: #f8fafc;">';
        echo '<tr>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Chassis</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Location</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Rack</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Position</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Type</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Slots</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Management IP</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>';
        echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';
        
        foreach ($chassies as $chassis) {
            echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
            echo '<td style="padding: 16px;">';
            echo '<div style="font-weight: 600; color: #111827;">' . htmlspecialchars($chassis->name) . '</div>';
            if ($chassis->hostname) {
                echo '<div style="font-size: 12px; color: #6b7280;">' . htmlspecialchars($chassis->hostname) . '</div>';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->location_name ? htmlspecialchars($chassis->location_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->rack_name ? htmlspecialchars($chassis->rack_name) : 'Unassigned') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">';
            if ($chassis->start_unit) {
                echo 'U' . $chassis->start_unit;
                if ($chassis->unit_size > 1) {
                    echo '-' . ($chassis->start_unit + $chassis->unit_size - 1);
                }
                echo ' (' . $chassis->unit_size . 'U)';
            } else {
                echo '-';
            }
            echo '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->chassis_type ? htmlspecialchars(ucfirst($chassis->chassis_type)) : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->slots ? $chassis->slots : '-') . '</td>';
            echo '<td style="padding: 16px; color: #6b7280;">' . ($chassis->management_ip ? htmlspecialchars($chassis->management_ip) : '-') . '</td>';
            echo '<td style="padding: 16px;">';
            $status_colors = [
                'online' => '#10b981',
                'offline' => '#6b7280', 
                'maintenance' => '#f59e0b',
                'provisioning' => '#3b82f6'
            ];
            $color = $status_colors[$chassis->status] ?? '#6b7280';
            echo '<span style="background: ' . $color . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">';
            echo ucfirst($chassis->status);
            echo '</span>';
            echo '</td>';
            echo '<td style="padding: 16px;">';
            echo '<div style="display: flex; gap: 8px;">';
            echo '<button onclick="editChassis(' . $chassis->id . ')" style="background: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
            echo '<i class="fas fa-edit"></i>';
            echo '</button>';
            if ($chassis->rack_id) {
                echo '<button onclick="viewInRack(' . $chassis->rack_id . ')" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">';
                echo '<i class="fas fa-eye"></i>';
                echo '</button>';
            }
            echo '</div>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-server"></i>';
        echo '<h3>No Chassies Found</h3>';
        echo '<p>Add your first chassis to get started</p>';
        echo '<button onclick="showAddChassisModal()" class="add-location-btn" style="margin-top: 16px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 6px;">';
        echo '<i class="fas fa-plus"></i> Add First Chassis';
        echo '</button>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    // Add hidden modal container for Add Chassis functionality
    echo '<div id="addChassisModalContainer" style="display: none;"></div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function editChassis(chassisId) {
        window.location.href = "' . $modulelink . '&action=chassies&edit=" + chassisId;
    }
    
    function viewInRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showAddChassisModal() {
        // Show loading state
        const modalContainer = document.getElementById("addChassisModalContainer");
        modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"font-size: 24px; color: #4299e1; margin-bottom: 16px;\"></i><p style=\"margin: 0; color: #6b7280;\">Loading chassis configuration...</p></div></div>";
        modalContainer.style.display = "block";
        
        // Load the modal content via AJAX
        fetch("' . $modulelink . '&action=chassies&ajax=1")
        .then(response => response.text())
        .then(html => {
            modalContainer.innerHTML = html;
            
            // Execute any scripts in the loaded content
            const scripts = modalContainer.querySelectorAll("script");
            scripts.forEach(script => {
                try {
                    if (script.src) {
                        // For external scripts, load them
                        const newScript = document.createElement("script");
                        newScript.src = script.src;
                        newScript.onload = function() {
                            // Script loaded successfully
                        };
                        document.head.appendChild(newScript);
                    } else {
                        // For inline scripts, execute them
                        const scriptContent = script.textContent || script.innerText;
                        new Function(scriptContent)();
                    }
                } catch (error) {
                    console.error("Error executing script:", error);
                }
            });
            
            // Initialize modal content after scripts are loaded
            setTimeout(function() {
                try {
                    // Force initialization of all modal functions
                    if (typeof initializeChassisAjaxModal === "function") {
                        initializeChassisAjaxModal();
                    }
                } catch (error) {
                    console.log("Could not initialize chassis modal content:", error);
                }
            }, 200);
        })
        .catch(error => {
            console.error("Error loading chassis interface:", error);
            modalContainer.innerHTML = "<div style=\"position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;\"><div style=\"background: white; border-radius: 12px; padding: 40px; text-align: center;\"><p style=\"margin: 0; color: #ef4444;\">Error loading chassis interface. Please try again.</p></div></div>";
        });
    }
    </script>';
}

/**
 * Manage chassis with bulk interface
 */
function dcim_manage_chassies($modulelink) {
    // Debug logging
    error_log("DCIM: ===== dcim_manage_chassies ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle bulk chassis addition
    error_log("DCIM: Checking for bulk chassis addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_chassies') {
        error_log("DCIM: BULK CHASSIS ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk chassis addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values
        try {
            Capsule::schema()->table('dcim_chassies', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated chassies rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify chassies rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['chassies']) || !is_array($_POST['chassies'])) {
            error_log("DCIM: No chassies data found in POST");
            echo '<div style="color: red;">No chassis data received. Please fill in at least one chassis label.</div>';
            return;
        }
        
        foreach ($_POST['chassies'] as $index => $chassis_data) {
            // Skip empty entries (must have at least a label)
            if (empty(trim($chassis_data['label'] ?? ''))) {
                continue;
            }
            
            $label = trim($chassis_data['label']);
            error_log("DCIM: Processing chassis: $label");
            
            try {
                Capsule::table('dcim_chassies')->insert([
                    'name' => $label,
                    'hostname' => $chassis_data['hostname'] ?? null,
                    'rack_id' => !empty($chassis_data['rack_id']) ? $chassis_data['rack_id'] : null,
                    'start_unit' => !empty($chassis_data['start_unit']) ? $chassis_data['start_unit'] : null,
                    'unit_size' => !empty($chassis_data['unit_size']) ? $chassis_data['unit_size'] : 10,
                    'make' => $chassis_data['model'] ?? null,
                    'model' => $chassis_data['model'] ?? null,
                    'status' => $chassis_data['status'] ?? 'offline',
                    'chassis_type' => 'blade',
                    'slots' => 8,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding chassis " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count chassies");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' chassi(es) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' chassies added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
                echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=chassies_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' chassi(es) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Get locations and racks for the interface
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
    } catch (Exception $e) {
        // Fallback data if database is not available
        $locations = collect([]);
        $racks = collect([]);
    }
    
    // Show the bulk addition interface
    dcim_render_bulk_chassis_interface($modulelink, $racks, $locations, $is_ajax);
}

/**
 * Render bulk chassis interface
 */
function dcim_render_bulk_chassis_interface($modulelink, $racks, $locations, $is_ajax = false) {
    // Get chassis models from database
    $chassis_models = dcim_get_chassis_models();
    
    // Define JavaScript functions early so they're available when HTML is rendered
    echo '<script>
    // Chassis model modal functions - defined immediately for global access
    function showAddChassisModelModal() {
        const modal = document.getElementById("addChassisModelModal");
        if (modal) {
            modal.style.display = "flex";
        } else {
            console.error("Chassis modal not found!");
        }
    }

    function closeAddChassisModelModal() {
        const modal = document.getElementById("addChassisModelModal");
        if (modal) {
            modal.style.display = "none";
            document.getElementById("addChassisModelForm").reset();
        }
    }

    function addChassisModel(formData) {
        fetch("' . $modulelink . '", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add to all chassis model dropdowns
                document.querySelectorAll(".chassis-model-select").forEach(select => {
                    const option = document.createElement("option");
                    option.value = data.name;
                    option.textContent = data.name;
                    select.appendChild(option);
                });
                closeAddChassisModelModal();
                if (typeof showToast === "function") {
                    showToast("Chassis model added successfully!", "success");
                }
            } else {
                if (typeof showToast === "function") {
                    showToast("Error adding chassis model: " + data.error, "error");
                }
            }
        })
        .catch(error => {
            if (typeof showToast === "function") {
                showToast("Network error: " + error.message, "error");
            }
        });
    }

    // Make functions globally available immediately
    window.showAddChassisModelModal = showAddChassisModelModal;
    window.closeAddChassisModelModal = closeAddChassisModelModal;
    window.addChassisModel = addChassisModel;
    
    // Also define closeModal for compatibility with existing buttons
    window.closeModal = function() {
        // Try to close any open modal
        const modals = document.querySelectorAll(\'[id*="Modal"]\');
        modals.forEach(modal => {
            if (modal.style.display === "flex" || modal.style.display === "block") {
                modal.style.display = "none";
            }
        });
        
        // Close the main bulk interface container if it exists
        const container = document.querySelector(\'[style*="position: fixed"]\');
        if (container && container.parentElement) {
            container.parentElement.style.display = "none";
        }
        
        // Also close the AJAX modal container
        const ajaxContainer = document.getElementById("addChassisModalContainer");
        if (ajaxContainer) {
            ajaxContainer.style.display = "none";
        }
    };
    
    // Initialize chassis AJAX modal functionality
    window.initializeChassisAjaxModal = function() {
        // Re-assign functions to ensure they work in AJAX context
        window.showAddChassisModelModal = showAddChassisModelModal;
        window.closeAddChassisModelModal = closeAddChassisModelModal;
        window.addChassisModel = addChassisModel;
        console.log("Chassis AJAX modal initialized - functions are now available");
    };
    </script>';
    
    // Modern modal-style container
    echo '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 1050; display: flex; align-items: center; justify-content: center; padding: 20px;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2); max-width: 1200px; width: 100%; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Modal header
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h2 style="margin: 0; font-size: 20px; font-weight: 600; color: #111827;">Bulk Add Chassies</h2>';
    echo '<button type="button" onclick="closeModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">&times;</button>';
    echo '</div>';
    
    // Tab navigation
    echo '<div style="padding: 0 32px; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">';
    echo '<ul class="nav nav-tabs" role="tablist" style="border: none; margin: 0; display: flex; gap: 32px;">';
    
    $tabs = [
        'basic-info' => ['icon' => 'fa-folder', 'label' => 'Basic Info'],
        'location' => ['icon' => 'fa-map-marker-alt', 'label' => 'Location']
    ];
    
    foreach ($tabs as $key => $tab) {
        $isActive = ($key === 'basic-info') ? 'active' : '';
        echo '<li role="presentation" class="' . $isActive . '" style="margin: 0; list-style: none;">';
        echo '<a href="#' . $key . '" aria-controls="' . $key . '" role="tab" data-toggle="tab" style="display: flex; align-items: center; gap: 8px; padding: 16px 0; border: none; background: none; color: ' . ($isActive ? '#4f46e5' : '#6b7280') . '; font-weight: 500; text-decoration: none; border-bottom: 3px solid ' . ($isActive ? '#4f46e5' : 'transparent') . '; margin-bottom: -1px;">';
        echo '<i class="fas ' . $tab['icon'] . '" style="font-size: 14px;"></i>';
        echo $tab['label'];
        echo '</a>';
        echo '</li>';
    }
    
    echo '</ul>';
    echo '</div>';
    
    // Form
    $form_onsubmit = $is_ajax ? 'onsubmit="console.log(\'Chassis form submit intercepted\'); return handleChassisAjaxSubmit(this, event)"' : '';
    echo '<form method="post" id="bulkChassisForm" ' . $form_onsubmit . ' style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    echo '<input type="hidden" name="action" value="bulk_add_chassies" id="actionField">';
    if ($is_ajax) {
        echo '<input type="hidden" name="ajax" value="1" id="ajaxField">';
    }
    
    // Tab content
    echo '<div class="tab-content" style="flex: 1; overflow: hidden; display: flex; flex-direction: column;">';
    
    // Basic Info Tab
    echo '<div role="tabpanel" class="tab-pane active" id="basic-info" style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">';
    
    // Top action buttons
    echo '<div style="padding: 20px 32px 0 32px; display: flex; gap: 12px; flex-shrink: 0;">';
    echo '<button type="button" onclick="addChassisRow()" style="background: #4f46e5; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; display: flex; align-items: center; gap: 6px;"><i class="fas fa-plus"></i> Add Row</button>';
    echo '<button type="button" onclick="addFiveChassisRows()" style="background: #059669; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; display: flex; align-items: center; gap: 6px;"><i class="fas fa-layer-group"></i> Add 5 Rows</button>';
    echo '</div>';
    
    // Table for Basic Info
    echo '<div style="flex: 1; overflow: auto; padding: 0 32px 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse; margin-top: 20px;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 60px;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 200px;">Label*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 180px;">Model*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 120px;">Status</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb; min-width: 80px;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    // Generate initial rows
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="chassies[' . ($i-1) . '][label]" class="form-control chassis-label-input" placeholder="Chassis Label" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<div style="display: flex; gap: 8px; align-items: center;">';
        echo '<select name="chassies[' . ($i-1) . '][model]" class="form-control chassis-model-select" style="flex: 1; border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px;">';
        echo '<option value="">Select Chassis Model</option>';
        foreach ($chassis_models as $model) {
            echo '<option value="' . htmlspecialchars($model->name) . '">' . htmlspecialchars($model->name) . '</option>';
        }
        echo '</select>';
        echo '<button type="button" class="btn btn-sm" onclick="showAddChassisModelModal()" title="Add Chassis Model" style="background: #5b21b6; color: white; border: none; padding: 8px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-plus"></i></button>';
        echo '</div>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][status]" class="form-control" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="available">Available</option>';
        echo '<option value="in_use">In Use</option>';
        echo '<option value="maintenance">Maintenance</option>';
        echo '<option value="offline">Offline</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeChassisRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    // Location Tab
    echo '<div role="tabpanel" class="tab-pane" id="location" style="flex: 1; display: none; flex-direction: column; overflow: hidden;">';
    
    // Group racks by city
    $racksByCity = [];
    foreach ($racks as $rack) {
        $city = $rack->location_name ?? 'Unknown';
        if (!isset($racksByCity[$city])) {
            $racksByCity[$city] = [];
        }
        $racksByCity[$city][] = $rack;
    }
    
    // Table for Location
    echo '<div style="flex: 1; overflow: auto; padding: 20px 32px;">';
    echo '<table style="width: 100%; border-collapse: collapse;">';
    echo '<thead style="background: #f8fafc; position: sticky; top: 0; z-index: 10;">';
    echo '<tr>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">#</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Label</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">City*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Rack*</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Top Position</th>';
    echo '<th style="padding: 16px; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    for ($i = 1; $i <= 10; $i++) {
        echo '<tr style="border-bottom: 1px solid #f3f4f6;">';
        echo '<td style="padding: 16px; color: #374151; font-weight: 500;">' . $i . '</td>';
        echo '<td style="padding: 16px; color: #6b7280; font-style: italic;" class="chassis-label-display">Device ' . $i . '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][city]" class="form-control city-select" onchange="updateChassisRackOptions(' . ($i-1) . ')" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select City</option>';
        foreach ($racksByCity as $city => $cityRacks) {
            echo '<option value="' . htmlspecialchars($city) . '">' . htmlspecialchars($city) . '</option>';
        }
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<select name="chassies[' . ($i-1) . '][rack_id]" class="form-control rack-select" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '<option value="">Select Rack</option>';
        echo '</select>';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<input type="text" name="chassies[' . ($i-1) . '][start_unit]" class="form-control" placeholder="e.g. 38" style="border: 1px solid #d1d5db; border-radius: 6px; padding: 8px 12px; font-size: 14px; width: 100%;">';
        echo '</td>';
        echo '<td style="padding: 16px;">';
        echo '<button type="button" class="btn btn-sm" onclick="removeChassisRow(this)" title="Delete" style="background: white; border: 1px solid #ef4444; color: #ef4444; padding: 6px 10px; border-radius: 6px; cursor: pointer;"><i class="fas fa-trash"></i></button>';
        echo '</td>';
        echo '</tr>';
    }
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>'; // End tab-content
    
    // Footer
    echo '<div style="padding: 24px 32px; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<div style="font-size: 14px; color: #6b7280;"><span id="chassisCount">0</span> Chassis configured</div>';
    echo '<div style="display: flex; gap: 12px;">';
    echo '<button type="button" onclick="closeModal()" style="background: white; border: 1px solid #d1d5db; color: #374151; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer;">Cancel</button>';
    echo '<button type="submit" style="background: #4f46e5; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 14px; cursor: pointer;"><i class="fas fa-plus-circle"></i> Add <span id="submitChassisCount">0</span> Chassis</button>';
    echo '</div>';
    echo '</div>';
    
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    // Add Chassis Model Modal
    echo '<div id="addChassisModelModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.6); z-index: 1100; align-items: center; justify-content: center;">';
    echo '<div style="background: white; border-radius: 12px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3); width: 100%; max-width: 500px; margin: 20px;">';
    echo '<div style="padding: 24px 32px; border-bottom: 1px solid #e5e7eb; display: flex; align-items: center; justify-content: space-between;">';
    echo '<h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Add Chassis Model</h3>';
    echo '<button type="button" onclick="closeAddChassisModelModal()" style="background: none; border: none; font-size: 24px; color: #6b7280; cursor: pointer;">&times;</button>';
    echo '</div>';
    echo '<form id="addChassisModelForm" style="padding: 24px 32px;">';
    echo '<div style="margin-bottom: 20px;">';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Model Name *</label>';
    echo '<input type="text" id="chassisModelName" placeholder="e.g., Dell PowerEdge M1000e" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>';
    echo '</div>';
    echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Manufacturer</label>';
    echo '<input type="text" id="chassisModelManufacturer" placeholder="e.g., Dell" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '<div>';
    echo '<label style="display: block; font-weight: 600; color: #374151; margin-bottom: 8px;">Description</label>';
    echo '<input type="text" id="chassisModelDescription" placeholder="Optional description" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">';
    echo '</div>';
    echo '</div>';
    echo '<div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">';
    echo '<button type="button" onclick="closeAddChassisModelModal()" style="padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 6px; background: white; color: #374151; cursor: pointer; font-size: 14px;">Cancel</button>';
    echo '<button type="submit" style="padding: 8px 16px; border: none; border-radius: 6px; background: #5b21b6; color: white; cursor: pointer; font-size: 14px;">Add Model</button>';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
     // Add CSS for tab functionality
     echo '<style>
     .tab-pane {
         display: none !important;
         flex-direction: column;
         overflow: hidden;
     }
     .tab-pane.active {
         display: flex !important;
         flex-direction: column;
         overflow: hidden;
     }
     .nav-tabs {
         border-bottom: 1px solid #e5e7eb;
     }
     .nav-tabs li {
         margin-bottom: -1px;
         list-style: none;
     }
     
     /* Default tab link styling */
     .nav-tabs li a {
         color: #6b7280 !important;
         border-bottom: 3px solid transparent !important;
         transition: all 0.2s ease;
     }
     
     /* Active tab styling */
     .nav-tabs li.active a,
     .nav-tabs li.active a:hover,
     .nav-tabs li.active a:focus {
         color: #4f46e5 !important;
         border-bottom-color: #4f46e5 !important;
         background: none !important;
     }
     
     /* Hover effect for non-active tabs */
     .nav-tabs li:not(.active) a:hover {
         color: #374151 !important;
         border-bottom-color: #d1d5db !important;
     }
     
     /* Ensure table containers take full width and height */
     .tab-pane > div {
         flex: 1;
         width: 100%;
     }
     
     .tab-pane table {
         width: 100%;
         min-width: 100%;
     }
     </style>';
     
     // Enhanced JavaScript for chassis
     echo '<script>
    // Global variables for chassis
    let currentChassisTabIndex = 0;
    const maxChassis = 10;
    const isChassisAjaxModal = ' . ($is_ajax ? 'true' : 'false') . ';
    
    // Available racks by city for chassis
    const chassisRacksByCity = ' . json_encode($racksByCity) . ';
    
    // Initialize chassis count and button state
    function updateChassisCount() {
        let count = 0;
        const chassisLabelInputs = document.querySelectorAll(".chassis-label-input");
        
        chassisLabelInputs.forEach(function(input) {
            if (input.value && input.value.trim() !== "") {
                count++;
            }
        });
        
        const countDisplay = document.getElementById("chassisCount");
        if (countDisplay) {
            countDisplay.textContent = count;
        }
        
        // Update submit button
        const submitBtn = document.querySelector("button[type=submit]");
        if (submitBtn) {
            const chassisText = count === 1 ? "Chassis" : "Chassis";
            
            if (count === 0) {
                submitBtn.disabled = true;
                submitBtn.style.opacity = "0.6";
                submitBtn.style.cursor = "not-allowed";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add 0 Chassiss";
                submitBtn.title = "Please fill in at least one chassis label";
            } else {
                submitBtn.disabled = false;
                submitBtn.style.opacity = "1";
                submitBtn.style.cursor = "pointer";
                submitBtn.innerHTML = "<i class=\"fas fa-plus-circle\"></i> Add <span id=\"submitChassisCount\">" + count + "</span> " + chassisText;
                submitBtn.title = "Add " + count + " chassi(es) to the database";
            }
        }
        
        console.log("Chassis count updated to:", count);
    }
    
    // Handle AJAX form submission for chassis
    function handleChassisAjaxSubmit(form, event) {
        console.log("handleChassisAjaxSubmit called, isChassisAjaxModal:", isChassisAjaxModal);
        
        if (!isChassisAjaxModal) {
            return true; // Allow normal submission
        }
        
        // Prevent normal form submission
        if (event) {
            event.preventDefault();
        }
        
        console.log("Preventing default submission, starting chassis AJAX submission");
        
        // Validate form before submission
        let hasValidChassis = false;
        let filledCount = 0;
        
        // Check each chassis row for at least a label
        for (let i = 0; i < 10; i++) {
            const labelInput = form.querySelector("input[name=\"chassies[" + i + "][label]\"]");
            
            if (!labelInput) {
                continue;
            }
            
            const label = labelInput.value ? labelInput.value.trim() : "";
            
            if (label !== "") {
                hasValidChassis = true;
                filledCount++;
            }
        }
        
        if (!hasValidChassis) {
            const errorDiv = document.createElement("div");
            errorDiv.style.cssText = "position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100; font-weight: 500; max-width: 400px;";
            errorDiv.innerHTML = "<i class=\"fas fa-exclamation-triangle\" style=\"margin-right: 8px;\"></i>Please fill in at least one chassis label.";
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
            
            console.log("Form validation failed: No chassis labels provided");
            return false;
        }
        
        console.log("Chassis form validation passed:", filledCount, "chassis with valid labels");
        
        // Show loading state
        const submitBtn = form.querySelector("button[type=submit]");
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> Adding Chassis...";
        submitBtn.disabled = true;
        
        // Prepare form data
        const formData = new FormData(form);
        
        // Ensure action field is included
        if (!formData.has("action")) {
            formData.append("action", "bulk_add_chassies");
        }
        
        console.log("Chassis form data being submitted");
        
        // Submit via AJAX
        const ajaxUrl = "' . $modulelink . '&action=chassies&ajax=1";
        console.log("Chassis AJAX URL:", ajaxUrl);
        
        fetch(ajaxUrl, {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            console.log("Chassis form submission response:", html.substring(0, 500));
            
            const isSuccess = html.includes("added successfully") || 
                             html.includes("position: fixed; top: 20px; right: 20px; background: #10b981") ||
                             html.includes("chassi(es) added successfully") ||
                             html.includes("ajaxSuccessIndicator") ||
                             html.includes("SUCCESS:");
            
            if (isSuccess) {
                console.log("Chassis success detected in response");
                const modalContainer = document.getElementById("addChassisModalContainer");
                modalContainer.innerHTML = html;
                
                setTimeout(function() {
                    closeModal();
                    window.location.reload();
                }, 2000);
            } else {
                console.log("Chassis submission completed but no clear success indicator");
                const modalContainer = document.getElementById("addChassisModalContainer");
                if (modalContainer) {
                    modalContainer.innerHTML = html;
                    
                    const scripts = modalContainer.querySelectorAll("script");
                    scripts.forEach(script => {
                        try {
                            if (script.textContent) {
                                new Function(script.textContent)();
                            }
                        } catch (e) {
                            console.error("Error executing script:", e);
                        }
                    });
                    
                    if (typeof initializeChassisAjaxModal === "function") {
                        initializeChassisAjaxModal();
                    }
                }
            }
        })
        .catch(error => {
            console.error("Chassis form submission error:", error);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            alert("Error submitting form. Please try again.");
        });
        
        return false;
    }
    

    
    
    // Chassis-specific functions
    function addChassisRow() {
        console.log("addChassisRow called");
        // Implementation would add dynamic rows
        updateChassisCount();
    }
    
    function addFiveChassisRows() {
        for (let i = 0; i < 5; i++) {
            addChassisRow();
        }
    }
    
    function removeChassisRow(button) {
        // Implementation for removing chassis rows
        console.log("removeChassisRow called");
    }
    

   
    function updateChassisRackOptions(chassisIndex) {
        const citySelect = document.querySelector("select[name=\'chassies[\' + chassisIndex + \'][city]\']");
        const rackSelect = document.querySelector("select[name=\'chassies[\' + chassisIndex + \'][rack_id]\']");
        
        if (!citySelect || !rackSelect) return;
        
        const selectedCity = citySelect.value;
        rackSelect.innerHTML = "<option value=\'\'>Select Rack</option>";
        
        if (selectedCity && chassisRacksByCity[selectedCity]) {
            chassisRacksByCity[selectedCity].forEach(function(rack) {
                const option = document.createElement("option");
                option.value = rack.id;
                option.textContent = rack.name + " (" + rack.units + " units)";
                rackSelect.appendChild(option);
            });
        }
    }
    
    // Sync chassis labels across tabs
    function syncChassisLabels() {
        const labelInputs = document.querySelectorAll(".chassis-label-input");
        const labelDisplays = document.querySelectorAll(".chassis-label-display");
        
        labelInputs.forEach(function(input, index) {
            labelDisplays.forEach(function(display, displayIndex) {
                if (displayIndex === index) {
                    const label = input.value.trim() || (display.textContent.includes("Device") ? "Device " + (index + 1) : "Chassis " + (index + 1));
                    display.textContent = label;
                }
            });
        });
    }
    
    // Attach listeners to inputs for real-time updates
    function attachChassisLabelListeners() {
        document.querySelectorAll(".chassis-label-input").forEach(function(input) {
            input.addEventListener("input", function() {
                updateChassisCount();
                syncChassisLabels();
            });
        });
    }
    
    // Initialize tab functionality
    document.addEventListener("DOMContentLoaded", function() {
        // Handle tab clicks
        document.querySelectorAll("[data-toggle=\'tab\']").forEach(function(tabLink) {
             tabLink.addEventListener("click", function(e) {
                 e.preventDefault();
                 
                 // Get target tab ID
                 const targetId = this.getAttribute("href").substring(1);
                 console.log("Switching to tab:", targetId);
                 
                 // Remove active class from all tabs and tab links
                 document.querySelectorAll(".tab-pane").forEach(function(pane) {
                     pane.classList.remove("active");
                     pane.style.display = "none";
                 });
                 document.querySelectorAll(".nav-tabs li").forEach(function(li) {
                     li.classList.remove("active");
                 });
                 document.querySelectorAll("[data-toggle=\'tab\']").forEach(function(link) {
                     link.style.color = "#6b7280";
                     link.style.borderBottomColor = "transparent";
                 });
                 
                 // Activate clicked tab
                 this.parentNode.classList.add("active");
                 this.style.color = "#4f46e5";
                 this.style.borderBottomColor = "#4f46e5";
                 
                 // Show target pane
                 const targetPane = document.getElementById(targetId);
                 if (targetPane) {
                     targetPane.classList.add("active");
                     targetPane.style.display = "flex";
                     targetPane.style.flexDirection = "column";
                     targetPane.style.overflow = "hidden";
                     console.log("Activated tab pane:", targetId);
                     
                     // Force a repaint to ensure proper display
                     setTimeout(function() {
                         const table = targetPane.querySelector("table");
                         if (table) {
                             table.style.width = "100%";
                             console.log("Table width set for tab:", targetId);
                         }
                     }, 50);
                 } else {
                     console.error("Target pane not found:", targetId);
                 }
             });
         });
        
         // Set initial tab state properly
         console.log("Setting initial tab state...");
         
         // First, hide all tabs
         document.querySelectorAll(".tab-pane").forEach(function(pane) {
             pane.classList.remove("active");
             pane.style.display = "none";
         });
         
         // Remove active from all tab links
         document.querySelectorAll(".nav-tabs li").forEach(function(li) {
             li.classList.remove("active");
         });
         document.querySelectorAll("[data-toggle=\'tab\']").forEach(function(tabLink) {
             tabLink.style.color = "#6b7280";
             tabLink.style.borderBottomColor = "transparent";
         });
         
         // Set basic-info as active
         const initialTab = document.getElementById("basic-info");
         const initialTabLink = document.querySelector("[href=\'#basic-info\']");
         
         if (initialTab && initialTabLink) {
             initialTab.classList.add("active");
             initialTab.style.display = "flex";
             initialTab.style.flexDirection = "column";
             initialTab.style.overflow = "hidden";
             
             initialTabLink.parentNode.classList.add("active");
             initialTabLink.style.color = "#4f46e5";
             initialTabLink.style.borderBottomColor = "#4f46e5";
             
             console.log("Initial tab set to basic-info");
         } else {
             console.error("Could not find initial tab elements");
         }
         
         // Initial count update
         updateChassisCount();
         syncChassisLabels();
         attachChassisLabelListeners();
         
         // Setup chassis model form submission
         const chassisModelForm = document.getElementById("addChassisModelForm");
         if (chassisModelForm) {
             chassisModelForm.addEventListener("submit", function(e) {
                 e.preventDefault();
                 const formData = {
                     ajax_action: "add_chassis_model",
                     name: document.getElementById("chassisModelName").value,
                     manufacturer: document.getElementById("chassisModelManufacturer").value,
                     description: document.getElementById("chassisModelDescription").value
                 };
                 addChassisModel(formData);
             });
         }
    });

    </script>';
}

/**
 * Manage switches with bulk interface - placeholder for now
 */
function dcim_manage_switches($modulelink) {
    // Debug logging
    error_log("DCIM: ===== dcim_manage_switches ENTRY POINT =====");
    error_log("DCIM: Function called with modulelink: $modulelink");
    
    $is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';
    
    // Enhanced debug logging
    error_log("DCIM: is_ajax = " . ($is_ajax ? 'true' : 'false'));
    error_log("DCIM: REQUEST_METHOD = " . $_SERVER['REQUEST_METHOD']);
    error_log("DCIM: POST action: " . ($_POST['action'] ?? 'none'));
    
    // Handle bulk switch addition
    error_log("DCIM: Checking for bulk switch addition. POST action = '" . ($_POST['action'] ?? 'not set') . "'");
    if ($_POST['action'] == 'bulk_add_switches') {
        error_log("DCIM: BULK SWITCH ADDITION TRIGGERED!");
        error_log("DCIM: Processing bulk switch addition - AJAX mode: " . ($is_ajax ? 'true' : 'false'));
        error_log("DCIM: POST data received: " . print_r($_POST, true));
        
        $success_count = 0;
        $error_count = 0;
        
        // First, make sure rack_id column allows NULL values
        try {
            Capsule::schema()->table('dcim_switches', function ($table) {
                $table->integer('rack_id')->nullable()->change();
            });
            error_log("DCIM: Updated switches rack_id column to allow NULL values");
        } catch (Exception $e) {
            error_log("DCIM: Could not modify switches rack_id column (might already be nullable): " . $e->getMessage());
        }
        
        if (!isset($_POST['switches']) || !is_array($_POST['switches'])) {
            error_log("DCIM: No switches data found in POST");
            echo '<div style="color: red;">No switch data received. Please fill in at least one switch name.</div>';
            return;
        }
        
        foreach ($_POST['switches'] as $index => $switch_data) {
            // Skip empty entries (must have at least a name)
            if (empty(trim($switch_data['name'] ?? ''))) {
                continue;
            }
            
            $label = trim($switch_data['name']);
            error_log("DCIM: Processing switch: $label");
            
            try {
                Capsule::table('dcim_switches')->insert([
                    'name' => $label,
                    'hostname' => $switch_data['hostname'] ?? null,
                    'rack_id' => !empty($switch_data['rack_id']) ? $switch_data['rack_id'] : null,
                    'start_unit' => !empty($switch_data['start_unit']) ? $switch_data['start_unit'] : null,
                    'unit_size' => !empty($switch_data['unit_size']) ? $switch_data['unit_size'] : 1,
                    'make' => $switch_data['make'] ?? null,
                    'model' => $switch_data['model'] ?? null,
                    'serial_number' => $switch_data['serial_number'] ?? null,
                    'ports' => !empty($switch_data['ports']) ? $switch_data['ports'] : 24,
                    'switch_type' => $switch_data['switch_type'] ?? 'edge',
                    'management_ip' => $switch_data['management_ip'] ?? null,
                    'power_consumption' => $switch_data['power_consumption'] ?? null,
                    'client_id' => $switch_data['client_id'] ?? null,
                    'service_id' => $switch_data['service_id'] ?? null,
                    'notes' => $switch_data['notes'] ?? null,
                    'status' => $switch_data['status'] ?? 'online',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $success_count++;
            } catch (Exception $e) {
                error_log("DCIM: Error adding switch " . $label . " - " . $e->getMessage());
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            error_log("DCIM: Successfully added $success_count switches");
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #10b981; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>';
            echo $success_count . ' switch(es) added successfully!';
            echo '</div>';
            
            // Add a clear success indicator for AJAX detection
            echo '<div id="ajaxSuccessIndicator" style="display: none;">SUCCESS: ' . $success_count . ' switches added successfully</div>';
            
            if ($is_ajax) {
                echo '<script>setTimeout(function() { if (typeof closeModal === "function") { closeModal(); } window.location.reload(); }, 2000);</script>';
                return; // Stop execution here for AJAX requests
            } else {
                echo '<script>setTimeout(function() { window.location.href = "' . $modulelink . '&action=switches_table"; }, 2000);</script>';
            }
        }
        if ($error_count > 0) {
            echo '<div style="position: fixed; top: 20px; right: 20px; background: #ef4444; color: white; padding: 16px 24px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1100;">';
            echo '<i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>';
            echo $error_count . ' switch(es) failed to add.';
            echo '</div>';
            if ($is_ajax) {
                return; // Stop execution here for AJAX requests with errors
            }
        }
        
        // For AJAX requests, return early after processing bulk addition
        if ($is_ajax) {
            return;
        }
    }
    
    // Get locations and racks for the interface
    try {
        $locations = Capsule::table('dcim_locations')->get();
        $racks = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->get();
    } catch (Exception $e) {
        // Fallback data if database is not available
        $locations = collect([]);
        $racks = collect([]);
    }
    
    // Show the bulk addition interface
    if ($is_ajax) {
        // For AJAX requests, only return the modal content
        dcim_render_bulk_switch_interface($modulelink, $racks, $locations, true);
        return; // Stop execution here for AJAX requests
    } else {
        // For regular requests, render the full page
        dcim_render_bulk_switch_interface($modulelink, $racks, $locations, false);
    }
}



/**
 * Modern Rack view with visual layout
 */
function dcim_modern_rack_view($modulelink) {
    $rack_id = $_GET['rack_id'];
    
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    try {
        $rack = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_racks.id', $rack_id)
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->first();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching rack: ' . $e->getMessage() . '</div>';
        return;
    }
    
    if (!$rack) {
        echo '<div class="alert alert-danger">Rack not found!</div>';
        return;
    }
    
    try {
        $servers = Capsule::table('dcim_servers')
            ->where('rack_id', $rack_id)
            ->orderBy('start_unit')
            ->get();
        
        $unassigned_servers = Capsule::table('dcim_servers')
            ->whereNull('rack_id')
            ->get();
        
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $servers = collect([]);
        $unassigned_servers = collect([]);
        $locations = collect([]);
    }
    
    // Add modern rack visualization CSS
    echo '<style>
    .rack-view-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        background: #f8fafc !important;
        min-height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .rack-layout {
        display: flex !important;
        min-height: 100vh !important;
    }
    
    .rack-sidebar,
    .dcim-sidebar {
        width: 280px !important;
        background: #ffffff !important;
        border-right: 1px solid #e2e8f0 !important;
        flex-shrink: 0 !important;
    }
    
    .rack-main {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        background: #f8fafc !important;
    }
    
    .rack-header {
        background: #ffffff !important;
        border-bottom: 1px solid #e2e8f0 !important;
        padding: 20px 24px !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .back-btn {
        background: none !important;
        border: none !important;
        color: #718096 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }
    
    .back-btn:hover {
        background: #f1f5f9 !important;
        color: #4a5568 !important;
    }
    
    .rack-title {
        font-size: 20px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
    }
    
    .rack-content {
        flex: 1 !important;
        padding: 24px !important;
        overflow: auto !important;
    }
    
    .unassigned-section {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        margin-bottom: 24px !important;
        overflow: hidden !important;
    }
    
    .unassigned-header {
        padding: 16px 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .unassigned-title {
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }
    
    .device-filters {
        display: flex !important;
        gap: 8px !important;
        margin-left: auto !important;
    }
    
    .filter-btn {
        background: #f7fafc !important;
        border: 1px solid #e2e8f0 !important;
        color: #4a5568 !important;
        padding: 6px 12px !important;
        border-radius: 6px !important;
        font-size: 13px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }
    
    .filter-btn.active {
        background: #4299e1 !important;
        color: white !important;
        border-color: #4299e1 !important;
    }
    
    .unassigned-content {
        padding: 20px !important;
        text-align: center !important;
        color: #718096 !important;
    }
    
    .rack-visualization {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 24px !important;
    }
    
    .rack-viz-header {
        text-align: center !important;
        margin-bottom: 24px !important;
    }
    
    .rack-viz-title {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 8px 0 !important;
    }
    
    .rack-info {
        font-size: 14px !important;
        color: #718096 !important;
        margin: 0 !important;
    }
    
    .rack-container {
        display: flex !important;
        justify-content: center !important;
        gap: 24px !important;
    }
    
    .rack-visual {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .rack-label {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #4a5568 !important;
    }
    
    .rack-frame {
        background: #f8fafc !important;
        border: 2px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 8px !important;
        position: relative !important;
        width: 300px !important;
    }
    
    .rack-unit {
        height: 24px !important;
        border: 1px solid #cbd5e0 !important;
        margin-bottom: 1px !important;
        display: flex !important;
        align-items: center !important;
        position: relative !important;
        background: #ffffff !important;
        border-radius: 3px !important;
        transition: all 0.2s ease !important;
    }
    
    .rack-unit:hover {
        border-color: #4299e1 !important;
        background: #ebf8ff !important;
    }
    
    .rack-unit.occupied {
        background: linear-gradient(135deg, #4299e1, #3182ce) !important;
        color: white !important;
        border-color: #3182ce !important;
        cursor: pointer !important;
    }
    
    .rack-unit.occupied.server {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        border-color: #059669 !important;
    }
    
    .rack-unit.occupied.switch {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        border-color: #d97706 !important;
    }
    
    .rack-unit.occupied.chassis {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
        border-color: #7c3aed !important;
    }
    
    .unit-number {
        position: absolute !important;
        left: 8px !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        color: #718096 !important;
        min-width: 20px !important;
    }
    
    .rack-unit.occupied .unit-number {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    
    .device-label {
        flex: 1 !important;
        text-align: center !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        padding: 0 8px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }
    
    .unit-size-indicator {
        position: absolute !important;
        right: 8px !important;
        font-size: 10px !important;
        font-weight: 600 !important;
        background: rgba(255, 255, 255, 0.2) !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
    }
    
        .rack-stats {
        margin-left: 24px !important;
        background: #f8fafc !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        width: 250px !important;
    }

    .stats-title {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 16px 0 !important;
    }


    
    .stat-item {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        font-size: 13px !important;
    }
    
    .stat-label {
        color: #718096 !important;
    }
    
    .stat-value {
        font-weight: 600 !important;
        color: #2d3748 !important;
    }
    
    .utilization-bar {
        width: 100% !important;
        height: 8px !important;
        background: #e2e8f0 !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        margin-top: 4px !important;
    }
    
    .utilization-fill {
        height: 100% !important;
        transition: width 0.3s ease !important;
    }
    
    .drag-target {
        border: 2px dashed #4299e1 !important;
        background: #ebf8ff !important;
    }
    
    .unassigned-device {
        background: #f7fafc !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 6px !important;
        padding: 12px !important;
        margin: 8px !important;
        cursor: grab !important;
        transition: all 0.2s ease !important;
        display: inline-block !important;
        min-width: 120px !important;
        text-align: center !important;
    }
    
    .unassigned-device:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border-color: #cbd5e0 !important;
    }
    
    .unassigned-device.dragging {
        opacity: 0.5 !important;
        transform: rotate(5deg) !important;
    }
    
    .device-name {
        font-weight: 600 !important;
        color: #2d3748 !important;
        font-size: 12px !important;
        margin-bottom: 4px !important;
    }
    
    .device-info {
        font-size: 10px !important;
        color: #718096 !important;
    }
    </style>';
    
    echo '<div class="rack-view-container">';
    echo '<div class="rack-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $rack->location_id);
    
    // Main content
    echo '<div class="rack-main">';
    echo '<div class="rack-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="rack-title">Rack Visualization</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-content">';
    
    // Unassigned devices section
    echo '<div class="unassigned-section">';
    echo '<div class="unassigned-header">';
    echo '<div class="unassigned-title">';
    echo '<i class="fas fa-info-circle"></i>';
    echo 'Unassigned Devices (' . count($unassigned_servers) . ')';
    echo '</div>';
    echo '<div style="font-size: 12px; color: #718096;">Drag devices to assign or unassign</div>';
    echo '<div class="device-filters">';
    echo '<button class="filter-btn active">All</button>';
    echo '<button class="filter-btn">Servers</button>';
    echo '<button class="filter-btn">Switches</button>';
    echo '<button class="filter-btn">Chassis</button>';
    echo '</div>';
    echo '</div>';
    echo '<div class="unassigned-content">';
    if (count($unassigned_servers) > 0) {
        foreach ($unassigned_servers as $device) {
            echo '<div class="unassigned-device" draggable="true" data-device-id="' . $device->id . '" data-device-size="' . $device->unit_size . '">';
            echo '<div class="device-name">' . htmlspecialchars($device->name) . '</div>';
            echo '<div class="device-info">' . $device->unit_size . 'U</div>';
            echo '</div>';
        }
    } else {
        echo 'No unassigned devices available';
    }
    echo '</div>';
    echo '</div>';
    
    // Rack visualization
    echo '<div class="rack-visualization">';
    echo '<div class="rack-viz-header">';
    echo '<div class="rack-viz-title">' . htmlspecialchars($rack->name) . '</div>';
    echo '<div class="rack-info">' . $rack->units . 'U • ' . htmlspecialchars($rack->location_name) . '</div>';
    echo '</div>';
    
    echo '<div class="rack-container">';
    echo '<div class="rack-visual">';
    echo '<div class="rack-label">Front of Rack</div>';
    echo '<div class="rack-frame" data-rack-id="' . $rack->id . '">';
    
    // Create array to track occupied units
    $occupied_units = [];
    foreach ($servers as $server) {
        if ($server->start_unit) {
            for ($i = $server->start_unit; $i < $server->start_unit + $server->unit_size; $i++) {
                $occupied_units[$i] = $server;
            }
        }
    }
    
    // Draw rack units from top to bottom
    for ($unit = $rack->units; $unit >= 1; $unit--) {
        if (isset($occupied_units[$unit])) {
            $server = $occupied_units[$unit];
            
            // Only draw server label on the first unit of multi-unit servers
            if ($server->start_unit == $unit) {
                $device_type = 'server'; // Default type, could be enhanced to detect type
                echo '<div class="rack-unit occupied ' . $device_type . '" style="height: ' . ($server->unit_size * 25) . 'px;" data-device-id="' . $server->id . '" title="' . htmlspecialchars($server->name) . '">';
                echo '<div class="unit-number">' . $unit . '</div>';
                echo '<div class="device-label">' . htmlspecialchars($server->name) . '</div>';
                echo '<div class="unit-size-indicator">' . $server->unit_size . 'U</div>';
                echo '</div>';
                
                // Skip the additional units for multi-unit devices
                $unit -= ($server->unit_size - 1);
            }
        } else {
            echo '<div class="rack-unit empty" data-unit="' . $unit . '">';
            echo '<div class="unit-number">' . $unit . '</div>';
            echo '</div>';
        }
    }
    
    echo '</div>';
    echo '</div>';
    
    // Rack statistics
    echo '<div class="rack-stats">';
    echo '<div class="stats-title">Rack Statistics</div>';
    
    $used_units = 0;
    $used_power = 0;
    $server_count = count($servers);
    
    try {
        $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack_id)->sum('unit_size');
        $used_power = Capsule::table('dcim_servers')->where('rack_id', $rack_id)->sum('power_consumption');
    } catch (Exception $e) {
        error_log("DCIM: Error calculating stats - " . $e->getMessage());
    }
    
    $unit_utilization = $rack->units > 0 ? ($used_units / $rack->units) * 100 : 0;
    $power_utilization = $rack->power_capacity > 0 ? ($used_power / $rack->power_capacity) * 100 : 0;
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Total Units:</span>';
    echo '<span class="stat-value">' . $rack->units . 'U</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Used Units:</span>';
    echo '<span class="stat-value">' . $used_units . 'U</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Unit Utilization:</span>';
    echo '<span class="stat-value">' . round($unit_utilization, 1) . '%</span>';
    echo '</div>';
    echo '<div class="utilization-bar">';
    echo '<div class="utilization-fill" style="width: ' . $unit_utilization . '%; background: ' . ($unit_utilization > 80 ? '#ef4444' : ($unit_utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
    echo '</div>';
    
    echo '<div class="stat-item" style="margin-top: 16px;">';
    echo '<span class="stat-label">Devices:</span>';
    echo '<span class="stat-value">' . $server_count . '</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Used:</span>';
    echo '<span class="stat-value">' . number_format($used_power) . 'W</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Capacity:</span>';
    echo '<span class="stat-value">' . number_format($rack->power_capacity) . 'W</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Utilization:</span>';
    echo '<span class="stat-value">' . round($power_utilization, 1) . '%</span>';
    echo '</div>';
    echo '<div class="utilization-bar">';
    echo '<div class="utilization-fill" style="width: ' . $power_utilization . '%; background: ' . ($power_utilization > 80 ? '#ef4444' : ($power_utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    
    // JavaScript for drag and drop functionality
    echo '<script>
    let draggedElement = null;
    
    // Drag and drop functionality
    document.querySelectorAll(".unassigned-device").forEach(device => {
        device.addEventListener("dragstart", function(e) {
            draggedElement = this;
            this.classList.add("dragging");
            e.dataTransfer.setData("text/plain", "");
        });
        
        device.addEventListener("dragend", function() {
            this.classList.remove("dragging");
            draggedElement = null;
        });
    });
    
    // Rack unit drop targets
    document.querySelectorAll(".rack-unit.empty").forEach(unit => {
        unit.addEventListener("dragover", function(e) {
            e.preventDefault();
            this.classList.add("drag-target");
        });
        
        unit.addEventListener("dragleave", function() {
            this.classList.remove("drag-target");
        });
        
        unit.addEventListener("drop", function(e) {
            e.preventDefault();
            this.classList.remove("drag-target");
            
            if (draggedElement) {
                const deviceId = draggedElement.dataset.deviceId;
                const deviceSize = parseInt(draggedElement.dataset.deviceSize);
                const unitNumber = parseInt(this.dataset.unit);
                const rackId = document.querySelector(".rack-frame").dataset.rackId;
                
                // Check if there is enough space
                if (canPlaceDevice(unitNumber, deviceSize)) {
                    assignDevice(deviceId, rackId, unitNumber);
                } else {
                    alert("Not enough consecutive empty units to place this device");
                }
            }
        });
    });
    
    // Occupied devices can be dragged back to unassigned
    document.querySelectorAll(".rack-unit.occupied").forEach(device => {
        device.addEventListener("dragstart", function(e) {
            draggedElement = this;
            e.dataTransfer.setData("text/plain", "");
        });
    });
    
    // Unassigned area as drop target
    document.querySelector(".unassigned-content").addEventListener("dragover", function(e) {
        e.preventDefault();
    });
    
    document.querySelector(".unassigned-content").addEventListener("drop", function(e) {
        e.preventDefault();
        
        if (draggedElement && draggedElement.classList.contains("occupied")) {
            const deviceId = draggedElement.dataset.deviceId;
            unassignDevice(deviceId);
        }
    });
    
    function canPlaceDevice(startUnit, deviceSize) {
        for (let i = 0; i < deviceSize; i++) {
            const unit = document.querySelector(`[data-unit="${startUnit - i}"]`);
            if (!unit || !unit.classList.contains("empty")) {
                return false;
            }
        }
        return true;
    }
    
    function assignDevice(deviceId, rackId, unit) {
        window.location.href = "' . $modulelink . '&action=assign_device&device_id=" + deviceId + "&rack_id=" + rackId + "&unit=" + unit;
    }
    
    function unassignDevice(deviceId) {
        if (confirm("Remove this device from the rack?")) {
            window.location.href = "' . $modulelink . '&action=assign_device&device_id=" + deviceId + "&rack_id=&unit=";
        }
    }
    
    function goBack() {
        window.location.href = "' . $modulelink . '&location_id=' . $rack->location_id . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    // Filter functionality
    document.querySelectorAll(".filter-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            document.querySelectorAll(".filter-btn").forEach(b => b.classList.remove("active"));
            this.classList.add("active");
            
            const filter = this.textContent.toLowerCase();
            // Add filtering logic here if needed
        });
    });
    </script>';
}

?>