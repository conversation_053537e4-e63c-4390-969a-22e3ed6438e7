<?php
/**
 * DCIM Database Table Initialization Script
 * 
 * This script manually creates all DCIM database tables.
 * Run this if you're having issues with automatic table creation.
 * 
 * Usage: Access this file directly via browser: /modules/addons/dcim/dcim-init-tables.php
 */

if (!defined("WHMCS")) {
    // Allow direct access for debugging
    require_once '../../../init.php';
}

use WHMCS\Database\Capsule;

echo '<h1>DCIM Database Table Initialization</h1>';
echo '<p>This script will create all necessary DCIM database tables.</p>';

try {
    // Check and create locations table
    if (!Capsule::schema()->hasTable('dcim_locations')) {
        Capsule::schema()->create('dcim_locations', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('contact_name')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->integer('total_power_capacity')->default(0);
            $table->string('power_unit')->default('Watts');
            $table->text('notes')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_locations table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_locations table already exists</div>';
    }

    // Check and create racks table
    if (!Capsule::schema()->hasTable('dcim_racks')) {
        Capsule::schema()->create('dcim_racks', function ($table) {
            $table->increments('id');
            $table->integer('location_id')->unsigned();
            $table->string('name');
            $table->integer('units')->default(42);
            $table->integer('power_capacity')->default(0);
            $table->string('power_unit')->default('Watts');
            $table->text('notes')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            
            // Add foreign key only if locations table exists
            if (Capsule::schema()->hasTable('dcim_locations')) {
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
            }
        });
        echo '<div style="color: green;">✓ Created dcim_racks table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_racks table already exists</div>';
    }

    // Check and create IPAM subnets table
    if (!Capsule::schema()->hasTable('dcim_subnets')) {
        Capsule::schema()->create('dcim_subnets', function ($table) {
            $table->increments('id');
            $table->integer('location_id')->unsigned()->nullable();
            $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
            $table->string('network'); // Network address, e.g., ***********
            $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
            $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->boolean('is_public')->default(false);
            $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
            $table->string('gateway')->nullable();
            $table->string('dns_primary')->nullable();
            $table->string('dns_secondary')->nullable();
            $table->string('vlan_id')->nullable();
            $table->text('note')->nullable();
            $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
            $table->timestamps();
            
            // Add foreign key to locations if it exists
            if (Capsule::schema()->hasTable('dcim_locations')) {
                $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
            }
            
            // Add unique constraint on subnet CIDR
            $table->unique(['subnet']);
        });
        echo '<div style="color: green;">✓ Created dcim_subnets table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_subnets table already exists</div>';
    }

    // Check and create IPAM IP addresses table
    if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
        Capsule::schema()->create('dcim_ip_addresses', function ($table) {
            $table->increments('id');
            $table->integer('subnet_id')->unsigned();
            $table->string('ip_address');
            $table->string('hostname')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
            $table->timestamps();
            
            // Add foreign key to subnets
            $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
            
            // Ensure each IP is unique within a subnet
            $table->unique(['subnet_id', 'ip_address']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_addresses table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_ip_addresses table already exists</div>';
    }

    // Check and create IPAM IP assignments table
    if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
        Capsule::schema()->create('dcim_ip_assignments', function ($table) {
            $table->increments('id');
            $table->integer('ip_address_id')->unsigned();
            $table->enum('device_type', ['server', 'switch', 'chassis']);
            $table->integer('device_id')->unsigned();
            $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
            $table->timestamps();
            
            // Add foreign key to IP addresses
            $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
            
            // Ensure one IP can't be assigned multiple times to the same interface
            $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_assignments table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_ip_assignments table already exists</div>';
    }

    // Check and create servers table
    if (!Capsule::schema()->hasTable('dcim_servers')) {
        Capsule::schema()->create('dcim_servers', function ($table) {
            $table->increments('id');
            $table->integer('rack_id')->unsigned()->nullable();
            $table->string('name');
            $table->string('hostname')->nullable();
            $table->integer('start_unit')->nullable();
            $table->integer('unit_size')->default(1);
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->text('specifications')->nullable();
            $table->integer('power_consumption')->default(0);
            $table->string('ip_address')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('service_id')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
            $table->timestamps();
            
            // Add foreign key only if racks table exists
            if (Capsule::schema()->hasTable('dcim_racks')) {
                $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
            }
        });
        echo '<div style="color: green;">✓ Created dcim_servers table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_servers table already exists</div>';
    }

    echo '<h2 style="color: green;">✓ Database initialization completed successfully!</h2>';

    // Create a sample subnet for testing
    if (isset($_GET['create_sample']) && $_GET['create_sample'] == '1') {
        try {
            $existing = Capsule::table('dcim_subnets')->where('subnet', '***********/24')->first();
            if (!$existing) {
                Capsule::table('dcim_subnets')->insert([
                    'subnet' => '***********/24',
                    'network' => '***********',
                    'prefix_length' => 24,
                    'ip_version' => 'IPv4',
                    'country' => 'Romania',
                    'city' => 'Bucharest',
                    'is_public' => false,
                    'subnet_type' => 'Root',
                    'note' => 'Sample subnet for testing',
                    'status' => 'Available',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                echo '<div style="color: green;">✓ Created sample subnet ***********/24</div>';
            } else {
                echo '<div style="color: blue;">✓ Sample subnet already exists</div>';
            }
        } catch (Exception $e) {
            echo '<div style="color: red;">Error creating sample subnet: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }

    echo '<p><a href="?create_sample=1">Create Sample Subnet</a> | <a href="?m=dcim&action=ipam&subaction=subnets">Go to IPAM Subnets</a></p>';

} catch (Exception $e) {
    echo '<div style="color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; margin: 10px 0;">';
    echo '<h3>Error creating tables:</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
    
    error_log("DCIM: Error in table initialization - " . $e->getMessage());
}

echo '<hr>';
echo '<h3>Current Database Tables:</h3>';
echo '<ul>';

$tables = ['dcim_locations', 'dcim_racks', 'dcim_subnets', 'dcim_ip_addresses', 'dcim_ip_assignments', 'dcim_servers'];
foreach ($tables as $table) {
    $exists = Capsule::schema()->hasTable($table);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? 'EXISTS' : 'MISSING';
    echo "<li style='color: $color;'>$table - $status</li>";
}

echo '</ul>';
?>
