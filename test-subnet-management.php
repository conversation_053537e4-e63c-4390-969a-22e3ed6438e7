<?php
/**
 * Test script for DCIM IPAM Subnet Management Features
 * 
 * This script tests the new subnet management functionality including:
 * - Subnet hierarchy support
 * - Allocation checking
 * - Subnet division
 * - IP generation with allocation awareness
 * - Smart deletion
 * - Subnet regeneration
 */

// Include WHMCS bootstrap
require_once __DIR__ . '/../../init.php';

use WHMCS\Database\Capsule;

// Include DCIM modules
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-ipam.php';

echo '<h1>DCIM IPAM Subnet Management Test</h1>';
echo '<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>';

// Test 1: Database Schema Validation
echo '<div class="test-section">';
echo '<h2>Test 1: Database Schema Validation</h2>';

try {
    // Check if parent_id column exists
    $hasParentId = Capsule::schema()->hasColumn('dcim_subnets', 'parent_id');
    if ($hasParentId) {
        echo '<div class="success">✓ parent_id column exists in dcim_subnets table</div>';
    } else {
        echo '<div class="error">✗ parent_id column missing from dcim_subnets table</div>';
    }
    
    // Check table structure
    $columns = Capsule::schema()->getColumnListing('dcim_subnets');
    echo '<div class="info">Available columns: ' . implode(', ', $columns) . '</div>';
    
} catch (Exception $e) {
    echo '<div class="error">Error checking schema: ' . $e->getMessage() . '</div>';
}

echo '</div>';

// Test 2: Allocation Checking Functions
echo '<div class="test-section">';
echo '<h2>Test 2: Allocation Checking Functions</h2>';

try {
    // Create a test subnet if it doesn't exist
    $testSubnet = Capsule::table('dcim_subnets')->where('subnet', '*************/24')->first();
    if (!$testSubnet) {
        $subnetId = Capsule::table('dcim_subnets')->insertGetId([
            'subnet' => '*************/24',
            'network' => '*************',
            'prefix_length' => 24,
            'ip_version' => 'IPv4',
            'subnet_type' => 'Root',
            'status' => 'Available',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        echo '<div class="info">Created test subnet *************/24 with ID: ' . $subnetId . '</div>';
    } else {
        $subnetId = $testSubnet->id;
        echo '<div class="info">Using existing test subnet with ID: ' . $subnetId . '</div>';
    }
    
    // Test allocation checking functions
    $hasAllocatedIps = dcim_subnet_has_allocated_ips($subnetId);
    $hasChildren = dcim_subnet_has_children($subnetId);
    $hasGeneratedIps = dcim_subnet_has_generated_ips($subnetId);
    
    echo '<div class="success">✓ dcim_subnet_has_allocated_ips() returned: ' . ($hasAllocatedIps ? 'true' : 'false') . '</div>';
    echo '<div class="success">✓ dcim_subnet_has_children() returned: ' . ($hasChildren ? 'true' : 'false') . '</div>';
    echo '<div class="success">✓ dcim_subnet_has_generated_ips() returned: ' . ($hasGeneratedIps ? 'true' : 'false') . '</div>';
    
    // Test status function
    $status = dcim_get_subnet_status($subnetId);
    echo '<div class="success">✓ dcim_get_subnet_status() returned status array</div>';
    echo '<div class="info">Can divide: ' . ($status['can_divide'] ? 'yes' : 'no') . '</div>';
    echo '<div class="info">Can generate IPs: ' . ($status['can_generate_ips'] ? 'yes' : 'no') . '</div>';
    echo '<div class="info">Can delete: ' . ($status['can_delete'] ? 'yes' : 'no') . '</div>';
    
} catch (Exception $e) {
    echo '<div class="error">Error testing allocation functions: ' . $e->getMessage() . '</div>';
}

echo '</div>';

// Test 3: IP Generation
echo '<div class="test-section">';
echo '<h2>Test 3: IP Generation with Allocation Awareness</h2>';

try {
    if (isset($subnetId)) {
        $result = dcim_generate_subnet_ips_safe($subnetId);
        if ($result['success']) {
            echo '<div class="success">✓ Successfully generated ' . $result['generated_count'] . ' IP addresses</div>';
            echo '<div class="info">Total possible IPs: ' . $result['total_possible'] . '</div>';
        } else {
            echo '<div class="info">IP generation result: ' . $result['error'] . '</div>';
        }
    }
} catch (Exception $e) {
    echo '<div class="error">Error testing IP generation: ' . $e->getMessage() . '</div>';
}

echo '</div>';

// Test 4: Subnet Division
echo '<div class="test-section">';
echo '<h2>Test 4: Subnet Division</h2>';

try {
    if (isset($subnetId)) {
        // Try to divide into /26 subnets
        $result = dcim_divide_subnet($subnetId, 26);
        if ($result['success']) {
            echo '<div class="success">✓ Successfully divided subnet into ' . $result['num_created'] . ' child subnets</div>';
            foreach ($result['created_subnets'] as $child) {
                echo '<div class="info">Created child subnet: ' . $child['subnet'] . '</div>';
            }
        } else {
            echo '<div class="info">Division result: ' . $result['error'] . '</div>';
        }
    }
} catch (Exception $e) {
    echo '<div class="error">Error testing subnet division: ' . $e->getMessage() . '</div>';
}

echo '</div>';

// Test 5: Subnet Regeneration
echo '<div class="test-section">';
echo '<h2>Test 5: Subnet Regeneration</h2>';

try {
    if (isset($subnetId)) {
        $result = dcim_regenerate_child_subnets($subnetId);
        if ($result['success']) {
            echo '<div class="success">✓ Regeneration completed</div>';
            echo '<div class="info">Regenerated: ' . $result['regenerated_count'] . ' subnets</div>';
            echo '<div class="info">Expected total: ' . $result['expected_total'] . '</div>';
            echo '<div class="info">Existing count: ' . $result['existing_count'] . '</div>';
        } else {
            echo '<div class="info">Regeneration result: ' . $result['error'] . '</div>';
        }
    }
} catch (Exception $e) {
    echo '<div class="error">Error testing subnet regeneration: ' . $e->getMessage() . '</div>';
}

echo '</div>';

echo '<div class="test-section">';
echo '<h2>Test Summary</h2>';
echo '<div class="info">All core functions have been tested. Check the results above for any errors.</div>';
echo '<div class="info">To test the full UI functionality, navigate to the IPAM module in your DCIM system.</div>';
echo '</div>';

?>
