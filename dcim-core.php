<?php
/**
 * DCIM Core Functions - Configuration, Database Setup & Utilities
 * 
 * @package    DCIM
 * <AUTHOR> Name
 * @copyright  2025
 * @version    1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Core configuration and database functions for DCIM addon
 * Note: dcim_config() function is defined in dcim-main.php to avoid conflicts
 */

/**
 * Ensure all database tables exist
 */
function dcim_ensure_tables_exist() {
    try {
        // Check if we need to update existing tables with new schema
        if (Capsule::schema()->hasTable('dcim_subnets')) {
            // Check if the subnet column exists (new schema)
            if (!Capsule::schema()->hasColumn('dcim_subnets', 'subnet')) {
                // Old schema detected, need to recreate tables
                error_log("DCIM: Old schema detected, tables need to be recreated");
                return false; // Let the reset script handle this
            }
        }

        // Check and create locations table
        if (!Capsule::schema()->hasTable('dcim_locations')) {
            Capsule::schema()->create('dcim_locations', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->text('address')->nullable();
                $table->string('city')->nullable();
                $table->string('country')->nullable();
                $table->string('contact_name')->nullable();
                $table->string('contact_email')->nullable();
                $table->string('contact_phone')->nullable();
                $table->integer('total_power_capacity')->default(0);
                $table->string('power_unit')->default('Watts');
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
            });
        }

        // Check and create racks table
        if (!Capsule::schema()->hasTable('dcim_racks')) {
            Capsule::schema()->create('dcim_racks', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('row')->nullable();
                $table->string('position')->nullable();
                $table->integer('units')->default(42);
                $table->integer('power_capacity')->default(0);
                $table->string('pdu_a')->nullable();
                $table->string('pdu_b')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['active', 'maintenance', 'inactive'])->default('active');
                $table->timestamps();
                
                // Add foreign key only if locations table exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
                }
            });
        }

        // Check and create CPU models table
        if (!Capsule::schema()->hasTable('dcim_cpu_models')) {
            Capsule::schema()->create('dcim_cpu_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('cores')->nullable();
                $table->string('frequency')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create RAM configurations table
        if (!Capsule::schema()->hasTable('dcim_ram_configs')) {
            Capsule::schema()->create('dcim_ram_configs', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('size')->nullable();
                $table->string('type')->nullable();
                $table->string('speed')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create switch models table
        if (!Capsule::schema()->hasTable('dcim_switch_models')) {
            Capsule::schema()->create('dcim_switch_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->string('model_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('port_speed')->nullable(); // 1G, 10G, etc.
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create chassis models table
        if (!Capsule::schema()->hasTable('dcim_chassis_models')) {
            Capsule::schema()->create('dcim_chassis_models', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('manufacturer')->nullable();
                $table->text('description')->nullable();
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
        }

        // Check and create servers table
        if (!Capsule::schema()->hasTable('dcim_servers')) {
            Capsule::schema()->create('dcim_servers', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->text('specifications')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->string('ip_address')->nullable();
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create switches table
        if (!Capsule::schema()->hasTable('dcim_switches')) {
            Capsule::schema()->create('dcim_switches', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(1);
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('ports')->default(24);
                $table->string('switch_type')->nullable(); // core, edge, tor, management
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create chassies table
        if (!Capsule::schema()->hasTable('dcim_chassies')) {
            Capsule::schema()->create('dcim_chassies', function ($table) {
                $table->increments('id');
                $table->integer('rack_id')->unsigned()->nullable();
                $table->string('name');
                $table->string('hostname')->nullable();
                $table->integer('start_unit')->nullable();
                $table->integer('unit_size')->default(10); // Chassis are typically larger
                $table->string('make')->nullable();
                $table->string('model')->nullable();
                $table->string('serial_number')->nullable();
                $table->integer('slots')->default(8); // Blade slots
                $table->string('chassis_type')->nullable(); // blade, storage, network
                $table->string('management_ip')->nullable();
                $table->integer('power_consumption')->default(0);
                $table->integer('client_id')->nullable();
                $table->integer('service_id')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
                $table->timestamps();
                
                // Add foreign key only if racks table exists
                if (Capsule::schema()->hasTable('dcim_racks')) {
                    $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
                }
            });
        }

        // Check and create IPAM subnets table
        if (!Capsule::schema()->hasTable('dcim_subnets')) {
            Capsule::schema()->create('dcim_subnets', function ($table) {
                $table->increments('id');
                $table->integer('location_id')->unsigned()->nullable();
                $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
                $table->string('network'); // Network address, e.g., ***********
                $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
                $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
                $table->string('country')->nullable();
                $table->string('city')->nullable();
                $table->boolean('is_public')->default(false);
                $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
                $table->string('gateway')->nullable();
                $table->string('dns_primary')->nullable();
                $table->string('dns_secondary')->nullable();
                $table->string('vlan_id')->nullable();
                $table->text('note')->nullable();
                $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
                $table->timestamps();

                // Add foreign key to locations if it exists
                if (Capsule::schema()->hasTable('dcim_locations')) {
                    $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
                }

                // Add unique constraint on subnet CIDR
                $table->unique(['subnet']);
            });
        }

        // Check and create IPAM IP addresses table
        if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
            Capsule::schema()->create('dcim_ip_addresses', function ($table) {
                $table->increments('id');
                $table->integer('subnet_id')->unsigned();
                $table->string('ip_address');
                $table->string('hostname')->nullable();
                $table->text('description')->nullable();
                $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
                $table->timestamps();
                
                // Add foreign key to subnets
                $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
                
                // Ensure each IP is unique within a subnet
                $table->unique(['subnet_id', 'ip_address']);
            });
        }

        // Check and create IPAM IP assignments table (links IPs to devices)
        if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
            Capsule::schema()->create('dcim_ip_assignments', function ($table) {
                $table->increments('id');
                $table->integer('ip_address_id')->unsigned();
                $table->enum('device_type', ['server', 'switch', 'chassis']);
                $table->integer('device_id')->unsigned();
                $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
                $table->timestamps();
                
                // Add foreign key to IP addresses
                $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
                
                // Ensure one IP can't be assigned multiple times to the same interface
                $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
            });
        }

        // Add foreign keys if tables exist but keys are missing
        dcim_add_missing_foreign_keys();

        // Populate default data
        dcim_populate_default_cpu_models();
        dcim_populate_default_ram_configs();
        dcim_populate_default_switch_models();
        dcim_populate_default_chassis_models();

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating tables - " . $e->getMessage());
        return false;
    }
}

/**
 * Add missing foreign keys if tables exist but foreign keys are missing
 */
function dcim_add_missing_foreign_keys() {
    try {
        // Check if racks table has foreign key to locations
        if (Capsule::schema()->hasTable('dcim_racks') && 
            Capsule::schema()->hasTable('dcim_locations')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

        // Check if servers table has foreign key to racks
        if (Capsule::schema()->hasTable('dcim_servers') && 
            Capsule::schema()->hasTable('dcim_racks')) {
            
            // This is a bit complex to check for existing foreign keys in Laravel
            // For now, we'll rely on the table creation to handle this
        }

    } catch (Exception $e) {
        error_log("DCIM: Error adding foreign keys - " . $e->getMessage());
    }
}

/**
 * Populate default CPU models
 */
function dcim_populate_default_cpu_models() {
    try {
        // Check if CPU models already exist
        $existing_count = Capsule::table('dcim_cpu_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $cpu_models = [
            ['name' => 'Intel Xeon E3-1230v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.5GHz'],
            ['name' => 'Intel Xeon E3-1270v6', 'manufacturer' => 'Intel', 'cores' => '4', 'frequency' => '3.8GHz'],
            ['name' => 'Intel Xeon E5-2620v4', 'manufacturer' => 'Intel', 'cores' => '8', 'frequency' => '2.1GHz'],
            ['name' => 'Intel Xeon E5-2630v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.2GHz'],
            ['name' => 'Intel Xeon E5-2640v4', 'manufacturer' => 'Intel', 'cores' => '10', 'frequency' => '2.4GHz'],
            ['name' => 'Intel Xeon Gold 5218', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.3GHz'],
            ['name' => 'Intel Xeon Gold 6242', 'manufacturer' => 'Intel', 'cores' => '16', 'frequency' => '2.8GHz'],
            ['name' => 'Intel Xeon Platinum 8280', 'manufacturer' => 'Intel', 'cores' => '28', 'frequency' => '2.7GHz'],
            ['name' => 'AMD EPYC 7302', 'manufacturer' => 'AMD', 'cores' => '16', 'frequency' => '3.0GHz'],
            ['name' => 'AMD EPYC 7402', 'manufacturer' => 'AMD', 'cores' => '24', 'frequency' => '2.8GHz'],
            ['name' => 'AMD EPYC 7502', 'manufacturer' => 'AMD', 'cores' => '32', 'frequency' => '2.5GHz'],
        ];

        foreach ($cpu_models as $cpu) {
            $cpu['created_at'] = date('Y-m-d H:i:s');
            $cpu['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_cpu_models')->insert($cpu);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating CPU models - " . $e->getMessage());
    }
}

/**
 * Populate default RAM configurations
 */
function dcim_populate_default_ram_configs() {
    try {
        // Check if RAM configs already exist
        $existing_count = Capsule::table('dcim_ram_configs')->count();
        if ($existing_count > 0) {
            return;
        }

        $ram_configs = [
            ['name' => '16GB DDR4', 'size' => '16GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '32GB DDR4', 'size' => '32GB', 'type' => 'DDR4', 'speed' => '2400MHz'],
            ['name' => '64GB DDR4', 'size' => '64GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '128GB DDR4', 'size' => '128GB', 'type' => 'DDR4', 'speed' => '2666MHz'],
            ['name' => '256GB DDR4', 'size' => '256GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '512GB DDR4', 'size' => '512GB', 'type' => 'DDR4', 'speed' => '2933MHz'],
            ['name' => '1TB DDR4', 'size' => '1TB', 'type' => 'DDR4', 'speed' => '3200MHz'],
        ];

        foreach ($ram_configs as $ram) {
            $ram['created_at'] = date('Y-m-d H:i:s');
            $ram['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_ram_configs')->insert($ram);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating RAM configs - " . $e->getMessage());
    }
}

/**
 * Populate default switch models
 */
function dcim_populate_default_switch_models() {
    try {
        // Check if switch models already exist
        $existing_count = Capsule::table('dcim_switch_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $switch_models = [
            ['name' => 'Cisco Catalyst 2960', 'manufacturer' => 'Cisco', 'model_number' => '2960', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3560', 'manufacturer' => 'Cisco', 'model_number' => '3560', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Cisco Catalyst 3750', 'manufacturer' => 'Cisco', 'model_number' => '3750', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Cisco Nexus 9300', 'manufacturer' => 'Cisco', 'model_number' => '9300', 'ports' => 48, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'HP ProCurve 2920', 'manufacturer' => 'HP', 'model_number' => '2920', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'HP ProCurve 5400', 'manufacturer' => 'HP', 'model_number' => '5400', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Juniper EX2300', 'manufacturer' => 'Juniper', 'model_number' => 'EX2300', 'ports' => 24, 'port_speed' => '1G', 'switch_type' => 'edge'],
            ['name' => 'Juniper EX4300', 'manufacturer' => 'Juniper', 'model_number' => 'EX4300', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'core'],
            ['name' => 'Arista 7050S', 'manufacturer' => 'Arista', 'model_number' => '7050S', 'ports' => 52, 'port_speed' => '10G', 'switch_type' => 'core'],
            ['name' => 'Dell PowerSwitch N1548', 'manufacturer' => 'Dell', 'model_number' => 'N1548', 'ports' => 48, 'port_speed' => '1G', 'switch_type' => 'edge'],
        ];

        foreach ($switch_models as $switch) {
            $switch['created_at'] = date('Y-m-d H:i:s');
            $switch['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_switch_models')->insert($switch);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating switch models - " . $e->getMessage());
    }
}

/**
 * Populate default chassis models
 */
function dcim_populate_default_chassis_models() {
    try {
        // Check if chassis models already exist
        $existing_count = Capsule::table('dcim_chassis_models')->count();
        if ($existing_count > 0) {
            return;
        }

        $chassis_models = [
            ['name' => 'Dell PowerEdge M1000e', 'manufacturer' => 'Dell'],
            ['name' => 'HP BladeSystem c7000', 'manufacturer' => 'HP'],
            ['name' => 'IBM BladeCenter H', 'manufacturer' => 'IBM'],
            ['name' => 'Cisco UCS 5108', 'manufacturer' => 'Cisco'],
            ['name' => 'Supermicro SuperBlade', 'manufacturer' => 'Supermicro'],
            ['name' => 'Dell PowerEdge VRTX', 'manufacturer' => 'Dell'],
            ['name' => 'HP ProLiant BL460c', 'manufacturer' => 'HP'],
            ['name' => 'NetApp FAS8200', 'manufacturer' => 'NetApp'],
            ['name' => 'EMC VNX5400', 'manufacturer' => 'EMC'],
            ['name' => 'Hitachi VSP G400', 'manufacturer' => 'Hitachi'],
        ];

        foreach ($chassis_models as $chassis) {
            $chassis['created_at'] = date('Y-m-d H:i:s');
            $chassis['updated_at'] = date('Y-m-d H:i:s');
            Capsule::table('dcim_chassis_models')->insert($chassis);
        }
    } catch (Exception $e) {
        error_log("DCIM: Error populating chassis models - " . $e->getMessage());
    }
}

/**
 * Get CPU models from database
 */
function dcim_get_cpu_models() {
    try {
        return Capsule::table('dcim_cpu_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching CPU models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get RAM configurations from database
 */
function dcim_get_ram_configs() {
    try {
        return Capsule::table('dcim_ram_configs')
            ->where('active', true)
            ->orderBy('size')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching RAM configs - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get switch models from database
 */
function dcim_get_switch_models() {
    try {
        return Capsule::table('dcim_switch_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching switch models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Get chassis models from database
 */
function dcim_get_chassis_models() {
    try {
        return Capsule::table('dcim_chassis_models')
            ->where('active', true)
            ->orderBy('manufacturer')
            ->orderBy('name')
            ->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching chassis models - " . $e->getMessage());
        return collect([]);
    }
}

/**
 * Handle AJAX requests for adding CPU models and RAM configs
 */
function dcim_handle_ajax_requests() {
    if ($_POST['ajax_action'] === 'add_cpu_model') {
        try {
            $id = Capsule::table('dcim_cpu_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'cores' => $_POST['cores'] ?? null,
                'frequency' => $_POST['frequency'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_ram_config') {
        try {
            $id = Capsule::table('dcim_ram_configs')->insertGetId([
                'name' => $_POST['name'],
                'size' => $_POST['size'] ?? null,
                'type' => $_POST['type'] ?? null,
                'speed' => $_POST['speed'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_switch_model') {
        try {
            $id = Capsule::table('dcim_switch_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    if ($_POST['ajax_action'] === 'add_chassis_model') {
        try {
            $id = Capsule::table('dcim_chassis_models')->insertGetId([
                'name' => $_POST['name'],
                'manufacturer' => $_POST['manufacturer'] ?? null,
                'description' => $_POST['description'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            echo json_encode(['success' => true, 'id' => $id, 'name' => $_POST['name']]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

/**
 * Create sample data for demo purposes
 */
function dcim_create_sample_data() {
    try {
        // Ensure tables exist first
        if (!dcim_ensure_tables_exist()) {
            return false;
        }

        // Check if data already exists
        $existing_locations = Capsule::table('dcim_locations')->count();
        if ($existing_locations > 0) {
            return true; // Data already exists
        }

        // Create sample locations
        $location_data = [
            [
                'name' => 'Primary Data Center',
                'address' => '123 Tech Street, Silicon Valley',
                'city' => 'San Jose',
                'country' => 'United States',
                'contact_name' => 'John Smith',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-0123',
                'total_power_capacity' => 50000,
                'power_unit' => 'Watts',
                'notes' => 'Primary data center facility',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'European Data Center',
                'address' => '456 Server Lane, Frankfurt',
                'city' => 'Frankfurt',
                'country' => 'Germany',
                'contact_name' => 'Anna Mueller',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+49-30-12345678',
                'total_power_capacity' => 30000,
                'power_unit' => 'Watts',
                'notes' => 'European operations center',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($location_data as $location) {
            $location_id = Capsule::table('dcim_locations')->insertGetId($location);

            // Create sample racks for each location
            $rack_data = [
                [
                    'location_id' => $location_id,
                    'name' => 'A01',
                    'row' => 'A',
                    'position' => '01',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A01-A',
                    'pdu_b' => 'PDU-A01-B',
                    'notes' => 'Primary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'location_id' => $location_id,
                    'name' => 'A02',
                    'row' => 'A',
                    'position' => '02',
                    'units' => 42,
                    'power_capacity' => 5000,
                    'pdu_a' => 'PDU-A02-A',
                    'pdu_b' => 'PDU-A02-B',
                    'notes' => 'Secondary server rack',
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            foreach ($rack_data as $rack) {
                $rack_id = Capsule::table('dcim_racks')->insertGetId($rack);

                // Create sample servers for the first rack only
                if ($rack['name'] == 'A01') {
                    $server_data = [
                        [
                            'rack_id' => $rack_id,
                            'name' => 'WEB-01',
                            'hostname' => 'web01.example.com',
                            'start_unit' => 40,
                            'unit_size' => 1,
                            'make' => 'Dell',
                            'model' => 'PowerEdge R640',
                            'serial_number' => 'DL001234',
                            'specifications' => '2x Intel Xeon Silver 4214, 64GB RAM, 2x 1TB SSD',
                            'power_consumption' => 300,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Web server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        [
                            'rack_id' => $rack_id,
                            'name' => 'DB-01',
                            'hostname' => 'db01.example.com',
                            'start_unit' => 37,
                            'unit_size' => 2,
                            'make' => 'HPE',
                            'model' => 'ProLiant DL380',
                            'serial_number' => 'HP005678',
                            'specifications' => '2x Intel Xeon Gold 6242, 128GB RAM, 4x 2TB SSD RAID 10',
                            'power_consumption' => 450,
                            'ip_address' => '************',
                            'client_id' => null,
                            'service_id' => null,
                            'notes' => 'Database server',
                            'status' => 'online',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]
                    ];

                    foreach ($server_data as $server) {
                        Capsule::table('dcim_servers')->insert($server);
                    }
                }
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("DCIM: Error creating sample data - " . $e->getMessage());
        return false;
    }
}

/**
 * Handle sample data creation request
 */
function dcim_handle_sample_data($modulelink) {
    if ($_GET['create_sample'] === 'true') {
        if (dcim_create_sample_data()) {
            echo '<div class="alert alert-success">Sample data created successfully! <a href="' . $modulelink . '">Refresh page</a></div>';
        } else {
            echo '<div class="alert alert-danger">Error creating sample data. Check error logs for details.</div>';
        }
        return true;
    }
    return false;
}

/**
 * Get country flag emoji based on country name
 */
function dcim_get_country_flag($country) {
    if (empty($country)) {
        return '🌍'; // Default globe icon
    }
    
    $country = strtolower(trim($country));
    
    $country_flags = [
        'united states' => '🇺🇸',
        'usa' => '🇺🇸',
        'us' => '🇺🇸',
        'germany' => '🇩🇪',
        'united kingdom' => '🇬🇧',
        'uk' => '🇬🇧',
        'france' => '🇫🇷',
        'canada' => '🇨🇦',
        'australia' => '🇦🇺',
        'japan' => '🇯🇵',
        'china' => '🇨🇳',
        'india' => '🇮🇳',
        'brazil' => '🇧🇷',
        'russia' => '🇷🇺',
        'south korea' => '🇰🇷',
        'italy' => '🇮🇹',
        'spain' => '🇪🇸',
        'netherlands' => '🇳🇱',
        'sweden' => '🇸🇪',
        'norway' => '🇳🇴',
        'denmark' => '🇩🇰',
        'finland' => '🇫🇮',
        'switzerland' => '🇨🇭',
        'austria' => '🇦🇹',
        'belgium' => '🇧🇪',
        'ireland' => '🇮🇪',
        'portugal' => '🇵🇹',
        'poland' => '🇵🇱',
        'czech republic' => '🇨🇿',
        'hungary' => '🇭🇺',
        'greece' => '🇬🇷',
        'turkey' => '🇹🇷',
        'israel' => '🇮🇱',
        'south africa' => '🇿🇦',
        'mexico' => '🇲🇽',
        'argentina' => '🇦🇷',
        'chile' => '🇨🇱',
        'colombia' => '🇨🇴',
        'peru' => '🇵🇪',
        'venezuela' => '🇻🇪',
        'thailand' => '🇹🇭',
        'vietnam' => '🇻🇳',
        'singapore' => '🇸🇬',
        'malaysia' => '🇲🇾',
        'indonesia' => '🇮🇩',
        'philippines' => '🇵🇭',
        'new zealand' => '🇳🇿',
        'ukraine' => '🇺🇦',
        'romania' => '🇷🇴',
        'bulgaria' => '🇧🇬',
        'croatia' => '🇭🇷',
        'slovenia' => '🇸🇮',
        'slovakia' => '🇸🇰',
        'lithuania' => '🇱🇹',
        'latvia' => '🇱🇻',
        'estonia' => '🇪🇪',
        'serbia' => '🇷🇸',
        'bosnia and herzegovina' => '🇧🇦',
        'montenegro' => '🇲🇪',
        'north macedonia' => '🇲🇰',
        'albania' => '🇦🇱',
        'luxembourg' => '🇱🇺',
        'iceland' => '🇮🇸',
        'malta' => '🇲🇹',
        'cyprus' => '🇨🇾',
        'monaco' => '🇲🇨',
        'liechtenstein' => '🇱🇮',
        'andorra' => '🇦🇩',
        'san marino' => '🇸🇲',
        'vatican city' => '🇻🇦',
        'egypt' => '🇪🇬',
        'morocco' => '🇲🇦',
        'tunisia' => '🇹🇳',
        'algeria' => '🇩🇿',
        'libya' => '🇱🇾',
        'nigeria' => '🇳🇬',
        'kenya' => '🇰🇪',
        'ghana' => '🇬🇭',
        'ethiopia' => '🇪🇹',
        'uganda' => '🇺🇬',
        'tanzania' => '🇹🇿',
        'cameroon' => '🇨🇲',
        'ivory coast' => '🇨🇮',
        'senegal' => '🇸🇳',
        'madagascar' => '🇲🇬',
        'botswana' => '🇧🇼',
        'namibia' => '🇳🇦',
        'zambia' => '🇿🇲',
        'zimbabwe' => '🇿🇼',
        'mozambique' => '🇲🇿',
        'malawi' => '🇲🇼',
        'rwanda' => '🇷🇼',
        'burundi' => '🇧🇮',
        'djibouti' => '🇩🇯',
        'eritrea' => '🇪🇷',
        'somalia' => '🇸🇴',
        'sudan' => '🇸🇩',
        'south sudan' => '🇸🇸',
        'chad' => '🇹🇩',
        'central african republic' => '🇨🇫',
        'democratic republic of the congo' => '🇨🇩',
        'republic of the congo' => '🇨🇬',
        'gabon' => '🇬🇦',
        'equatorial guinea' => '🇬🇶',
        'sao tome and principe' => '🇸🇹',
        'cape verde' => '🇨🇻',
        'guinea' => '🇬🇳',
        'guinea-bissau' => '🇬🇼',
        'sierra leone' => '🇸🇱',
        'liberia' => '🇱🇷',
        'burkina faso' => '🇧🇫',
        'mali' => '🇲🇱',
        'niger' => '🇳🇪',
        'mauritania' => '🇲🇷',
        'the gambia' => '🇬🇲',
        'gambia' => '🇬🇲',
        'mauritius' => '🇲🇺',
        'seychelles' => '🇸🇨',
        'comoros' => '🇰🇲',
        'lesotho' => '🇱🇸',
        'eswatini' => '🇸🇿',
        'swaziland' => '🇸🇿'
    ];
    
    return $country_flags[$country] ?? '🌍';
}

/**
 * IPAM Utility Functions
 */

/**
 * Validate IP address format
 */
function dcim_validate_ip($ip, $version = 'IPv4') {
    if ($version === 'IPv4') {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) !== false;
    } else {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6) !== false;
    }
}

/**
 * Validate CIDR notation
 */
function dcim_validate_cidr($cidr, $version = 'IPv4') {
    if (!preg_match('/^(.+)\/(\d+)$/', $cidr, $matches)) {
        return false;
    }

    $ip = $matches[1];
    $prefix = (int)$matches[2];

    if (!dcim_validate_ip($ip, $version)) {
        return false;
    }

    if ($version === 'IPv4') {
        return $prefix >= 0 && $prefix <= 32;
    } else {
        return $prefix >= 0 && $prefix <= 128;
    }
}

/**
 * Calculate network address from CIDR
 */
function dcim_calculate_network($cidr) {
    if (!preg_match('/^(.+)\/(\d+)$/', $cidr, $matches)) {
        return false;
    }

    $ip = $matches[1];
    $prefix = (int)$matches[2];

    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $ip_long = ip2long($ip);
        $mask = -1 << (32 - $prefix);
        $network_long = $ip_long & $mask;
        return long2ip($network_long);
    }

    // IPv6 support would go here
    return false;
}

/**
 * Calculate total IPs in subnet
 */
function dcim_calculate_total_ips($prefix_length, $version = 'IPv4') {
    if ($version === 'IPv4') {
        return pow(2, 32 - $prefix_length);
    } else {
        return pow(2, 128 - $prefix_length);
    }
}

/**
 * Generate IP addresses for a subnet
 */
function dcim_generate_subnet_ips($subnet_id, $network, $prefix_length) {
    try {
        $ip_long = ip2long($network);
        $total_ips = pow(2, 32 - $prefix_length);

        // Don't generate more than 1000 IPs at once for performance
        $max_ips = min($total_ips, 1000);

        $ips = [];
        for ($i = 0; $i < $max_ips; $i++) {
            $ip = long2ip($ip_long + $i);
            $status = 'available';

            // First IP is usually network address
            if ($i === 0) {
                $status = 'reserved';
            }
            // Last IP is usually broadcast address
            if ($i === $total_ips - 1 && $prefix_length < 31) {
                $status = 'reserved';
            }

            $ips[] = [
                'subnet_id' => $subnet_id,
                'ip_address' => $ip,
                'status' => $status,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        // Batch insert for performance
        if (!empty($ips)) {
            Capsule::table('dcim_ip_addresses')->insert($ips);
        }

        return count($ips);
    } catch (Exception $e) {
        error_log("DCIM: Error generating subnet IPs - " . $e->getMessage());
        return false;
    }
}

/**
 * Create a new subnet
 */
function dcim_create_subnet($data) {
    try {
        // Validate CIDR
        if (!dcim_validate_cidr($data['subnet'], $data['ip_version'])) {
            return ['success' => false, 'error' => 'Invalid CIDR notation'];
        }

        // Extract network and prefix
        list($network, $prefix_length) = explode('/', $data['subnet']);
        $network = dcim_calculate_network($data['subnet']);

        // Check if subnet already exists
        $existing = Capsule::table('dcim_subnets')
            ->where('subnet', $data['subnet'])
            ->first();

        if ($existing) {
            return ['success' => false, 'error' => 'Subnet already exists'];
        }

        // Create subnet
        $subnet_id = Capsule::table('dcim_subnets')->insertGetId([
            'subnet' => $data['subnet'],
            'network' => $network,
            'prefix_length' => $prefix_length,
            'ip_version' => $data['ip_version'],
            'location_id' => !empty($data['location_id']) ? $data['location_id'] : null,
            'country' => $data['country'] ?? null,
            'city' => $data['city'] ?? null,
            'is_public' => $data['is_public'] ?? false,
            'subnet_type' => $data['subnet_type'] ?? 'Root',
            'note' => $data['note'] ?? null,
            'status' => 'Available',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Generate IP addresses for the subnet
        if ($data['ip_version'] === 'IPv4') {
            dcim_generate_subnet_ips($subnet_id, $network, $prefix_length);
        }

        return ['success' => true, 'subnet_id' => $subnet_id];
    } catch (Exception $e) {
        error_log("DCIM: Error creating subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Delete a subnet and all its IP addresses
 */
function dcim_delete_subnet($subnet_id) {
    try {
        // Delete all IP addresses first (cascade should handle this, but being explicit)
        Capsule::table('dcim_ip_addresses')->where('subnet_id', $subnet_id)->delete();

        // Delete the subnet
        $deleted = Capsule::table('dcim_subnets')->where('id', $subnet_id)->delete();

        return ['success' => true, 'deleted' => $deleted > 0];
    } catch (Exception $e) {
        error_log("DCIM: Error deleting subnet - " . $e->getMessage());
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

/**
 * Core module functions for DCIM addon
 * Note: dcim_activate(), dcim_deactivate(), and dcim_upgrade() functions
 * are defined in dcim-main.php to avoid conflicts
 */

?>