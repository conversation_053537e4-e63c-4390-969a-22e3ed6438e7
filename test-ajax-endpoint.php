<?php
/**
 * Test AJAX endpoint directly
 */

// Include WHMCS environment
require_once __DIR__ . '/../../init.php';
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-ipam.php';

use WHMCS\Database\Capsule;

echo '<h1>Testing AJAX Endpoint</h1>';

// First, check if we have any subnets
$subnets = Capsule::table('dcim_subnets')->get();
echo '<h2>Available Subnets:</h2>';

if (count($subnets) > 0) {
    echo '<ul>';
    foreach ($subnets as $subnet) {
        echo '<li>ID: ' . $subnet->id . ' - ' . htmlspecialchars($subnet->subnet) . '</li>';
    }
    echo '</ul>';

    $test_subnet = $subnets[0];
    echo '<h2>Testing AJAX call for subnet ID: ' . $test_subnet->id . '</h2>';

    // Test the AJAX handler directly
    echo '<h3>Direct AJAX Handler Test:</h3>';
    ob_start();
    dcim_handle_subnet_details_ajax($test_subnet->id);
    $ajax_output = ob_get_clean();

    echo '<pre>' . htmlspecialchars($ajax_output) . '</pre>';

    // Test JSON parsing
    $response = json_decode($ajax_output, true);
    if ($response) {
        echo '<h3>✅ JSON Response Parsed Successfully:</h3>';
        echo '<pre>' . print_r($response, true) . '</pre>';
    } else {
        echo '<h3>❌ JSON Parse Failed:</h3>';
        echo '<p>Error: ' . json_last_error_msg() . '</p>';
    }

} else {
    echo '<p>No subnets found. Please create a subnet first.</p>';
}
?>
