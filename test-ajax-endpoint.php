<?php
/**
 * Test AJAX endpoint directly
 */

// Include WHMCS environment
require_once __DIR__ . '/../../init.php';
require_once __DIR__ . '/dcim-core.php';
require_once __DIR__ . '/dcim-ipam.php';

use WHMCS\Database\Capsule;

// Simulate the AJAX request
$_GET['action'] = 'ipam';
$_GET['subaction'] = 'subnet_details';
$_GET['subnet_id'] = '1'; // Test with subnet ID 1

// Set up a fake modulelink
$modulelink = 'addonmodules.php?module=dcim';

// Call the IPAM dashboard function which should handle the AJAX
dcim_ipam_dashboard($modulelink);
?>
