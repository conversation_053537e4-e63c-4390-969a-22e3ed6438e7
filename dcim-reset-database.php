<?php
/**
 * DCIM Database Reset Script
 * 
 * This script drops all existing DCIM tables and recreates them with the correct schema.
 * Use this when you need to reset the database structure.
 * 
 * Usage: Access this file directly via browser: /modules/addons/dcim/dcim-reset-database.php
 */

if (!defined("WHMCS")) {
    // Allow direct access for debugging
    require_once '../../../init.php';
}

use WHMCS\Database\Capsule;

echo '<h1>DCIM Database Reset</h1>';
echo '<p>This script will drop all existing DCIM tables and recreate them with the correct schema.</p>';

if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    try {
        echo '<h2>Step 1: Dropping existing tables...</h2>';
        
        // List of tables to drop in correct order (respecting foreign keys)
        $tables_to_drop = [
            'dcim_ip_assignments',
            'dcim_ip_addresses', 
            'dcim_subnets',
            'dcim_servers',
            'dcim_switches',
            'dcim_chassies',
            'dcim_racks',
            'dcim_locations',
            'dcim_cpu_models',
            'dcim_ram_configs',
            'dcim_switch_models',
            'dcim_chassis_models'
        ];
        
        // Disable foreign key checks temporarily
        Capsule::statement('SET FOREIGN_KEY_CHECKS=0');
        
        foreach ($tables_to_drop as $table) {
            if (Capsule::schema()->hasTable($table)) {
                Capsule::schema()->drop($table);
                echo "<div style='color: orange;'>✓ Dropped table: $table</div>";
            } else {
                echo "<div style='color: gray;'>- Table $table does not exist</div>";
            }
        }
        
        // Re-enable foreign key checks
        Capsule::statement('SET FOREIGN_KEY_CHECKS=1');
        
        echo '<h2>Step 2: Creating tables with correct schema...</h2>';
        
        // Create locations table
        Capsule::schema()->create('dcim_locations', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('contact_name')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->integer('total_power_capacity')->default(0);
            $table->string('power_unit')->default('Watts');
            $table->text('notes')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_locations table</div>';
        
        // Create racks table
        Capsule::schema()->create('dcim_racks', function ($table) {
            $table->increments('id');
            $table->integer('location_id')->unsigned();
            $table->string('name');
            $table->integer('units')->default(42);
            $table->integer('power_capacity')->default(0);
            $table->string('power_unit')->default('Watts');
            $table->text('notes')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('cascade');
        });
        echo '<div style="color: green;">✓ Created dcim_racks table</div>';
        
        // Create CPU models table
        Capsule::schema()->create('dcim_cpu_models', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->string('manufacturer')->nullable();
            $table->string('cores')->nullable();
            $table->string('frequency')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_cpu_models table</div>';
        
        // Create RAM configs table
        Capsule::schema()->create('dcim_ram_configs', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->string('capacity')->nullable();
            $table->string('type')->nullable();
            $table->string('speed')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_ram_configs table</div>';
        
        // Create switch models table
        Capsule::schema()->create('dcim_switch_models', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->string('manufacturer')->nullable();
            $table->integer('ports')->nullable();
            $table->string('port_type')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_switch_models table</div>';
        
        // Create chassis models table
        Capsule::schema()->create('dcim_chassis_models', function ($table) {
            $table->increments('id');
            $table->string('name');
            $table->string('manufacturer')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
        echo '<div style="color: green;">✓ Created dcim_chassis_models table</div>';
        
        // Create servers table
        Capsule::schema()->create('dcim_servers', function ($table) {
            $table->increments('id');
            $table->integer('rack_id')->unsigned()->nullable();
            $table->string('name');
            $table->string('hostname')->nullable();
            $table->integer('start_unit')->nullable();
            $table->integer('unit_size')->default(1);
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->text('specifications')->nullable();
            $table->integer('power_consumption')->default(0);
            $table->string('ip_address')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('service_id')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['online', 'offline', 'maintenance', 'provisioning'])->default('offline');
            $table->timestamps();
            $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
        });
        echo '<div style="color: green;">✓ Created dcim_servers table</div>';
        
        // Create switches table
        Capsule::schema()->create('dcim_switches', function ($table) {
            $table->increments('id');
            $table->integer('rack_id')->unsigned()->nullable();
            $table->string('name');
            $table->string('hostname')->nullable();
            $table->integer('start_unit')->nullable();
            $table->integer('unit_size')->default(1);
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->integer('ports')->nullable();
            $table->string('management_ip')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['online', 'offline', 'maintenance'])->default('offline');
            $table->timestamps();
            $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
        });
        echo '<div style="color: green;">✓ Created dcim_switches table</div>';
        
        // Create chassis table
        Capsule::schema()->create('dcim_chassies', function ($table) {
            $table->increments('id');
            $table->integer('rack_id')->unsigned()->nullable();
            $table->string('name');
            $table->string('hostname')->nullable();
            $table->integer('start_unit')->nullable();
            $table->integer('unit_size')->default(1);
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('serial_number')->nullable();
            $table->integer('slots')->nullable();
            $table->string('management_ip')->nullable();
            $table->text('notes')->nullable();
            $table->enum('status', ['online', 'offline', 'maintenance'])->default('offline');
            $table->timestamps();
            $table->foreign('rack_id')->references('id')->on('dcim_racks')->onDelete('set null');
        });
        echo '<div style="color: green;">✓ Created dcim_chassies table</div>';
        
        // Create IPAM subnets table with correct schema
        Capsule::schema()->create('dcim_subnets', function ($table) {
            $table->increments('id');
            $table->integer('location_id')->unsigned()->nullable();
            $table->string('subnet'); // Full CIDR notation, e.g., ***********/24
            $table->string('network'); // Network address, e.g., ***********
            $table->integer('prefix_length'); // CIDR prefix length, e.g., 24
            $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->boolean('is_public')->default(false);
            $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
            $table->string('gateway')->nullable();
            $table->string('dns_primary')->nullable();
            $table->string('dns_secondary')->nullable();
            $table->string('vlan_id')->nullable();
            $table->text('note')->nullable();
            $table->enum('status', ['Available', 'Parent of Allocated Subnet', 'Reserved', 'Deprecated'])->default('Available');
            $table->timestamps();
            $table->foreign('location_id')->references('id')->on('dcim_locations')->onDelete('set null');
            $table->unique(['subnet']);
        });
        echo '<div style="color: green;">✓ Created dcim_subnets table with correct schema</div>';
        
        // Create IPAM IP addresses table
        Capsule::schema()->create('dcim_ip_addresses', function ($table) {
            $table->increments('id');
            $table->integer('subnet_id')->unsigned();
            $table->string('ip_address');
            $table->string('hostname')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
            $table->timestamps();
            $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
            $table->unique(['subnet_id', 'ip_address']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_addresses table</div>';
        
        // Create IPAM IP assignments table
        Capsule::schema()->create('dcim_ip_assignments', function ($table) {
            $table->increments('id');
            $table->integer('ip_address_id')->unsigned();
            $table->enum('device_type', ['server', 'switch', 'chassis']);
            $table->integer('device_id')->unsigned();
            $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
            $table->timestamps();
            $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
            $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_assignments table</div>';
        
        echo '<h2 style="color: green;">✅ Database reset completed successfully!</h2>';
        echo '<p>All tables have been recreated with the correct schema.</p>';
        echo '<p><a href="?m=dcim&action=ipam&subaction=subnets">Go to IPAM Subnets</a></p>';
        
    } catch (Exception $e) {
        echo '<div style="color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; margin: 10px 0;">';
        echo '<h3>Error during database reset:</h3>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
        error_log("DCIM: Error in database reset - " . $e->getMessage());
    }
    
} else {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0;">';
    echo '<h3>⚠️ Warning</h3>';
    echo '<p>This will <strong>permanently delete</strong> all existing DCIM data and recreate the tables.</p>';
    echo '<p>Since you mentioned there is no data, this is safe to proceed.</p>';
    echo '</div>';
    
    echo '<p><a href="?confirm=yes" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Yes, Reset Database</a></p>';
    echo '<p><a href="?m=dcim">Cancel and go back to DCIM</a></p>';
}
?>
