{* DCIM Client Area Template *}

<link rel="stylesheet" type="text/css" href="modules/addons/dcim/assets/dcim.css" />

<div class="dcim-clientarea">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-server"></i> My Data Center Infrastructure
                    </h3>
                </div>
                <div class="panel-body">
                    {if $servers|count > 0}
                        <div class="row">
                            <div class="col-md-12">
                                <p class="text-muted">Below are your assigned servers across our data center locations. Click on any server for detailed information.</p>
                            </div>
                        </div>
                        
                        <div class="row">
                            {foreach $servers as $server}
                                <div class="col-md-6 col-lg-4">
                                    <div class="dcim-server-card">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="card-title">
                                                    <i class="fa fa-server"></i> {$server->name}
                                                </h5>
                                                <span class="badge badge-{if $server->status == 'online'}success{elseif $server->status == 'offline'}secondary{elseif $server->status == 'maintenance'}warning{else}info{/if}">
                                                    {$server->status|ucfirst}
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="server-details">
                                                    <div class="detail-row">
                                                        <strong>Location:</strong>
                                                        <span>{$server->location_name}</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <strong>Rack:</strong>
                                                        <span>{$server->rack_name}</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <strong>Position:</strong>
                                                        <span>U{$server->start_unit}{if $server->unit_size > 1} - U{$server->start_unit + $server->unit_size - 1}{/if}</span>
                                                    </div>
                                                    {if $server->hostname}
                                                    <div class="detail-row">
                                                        <strong>Hostname:</strong>
                                                        <span>{$server->hostname}</span>
                                                    </div>
                                                    {/if}
                                                    {if $server->ip_address}
                                                    <div class="detail-row">
                                                        <strong>IP Address:</strong>
                                                        <span><code>{$server->ip_address}</code></span>
                                                    </div>
                                                    {/if}
                                                    {if $server->make || $server->model}
                                                    <div class="detail-row">
                                                        <strong>Hardware:</strong>
                                                        <span>{$server->make} {$server->model}</span>
                                                    </div>
                                                    {/if}
                                                    {if $server->power_consumption > 0}
                                                    <div class="detail-row">
                                                        <strong>Power:</strong>
                                                        <span>{$server->power_consumption}W</span>
                                                    </div>
                                                    {/if}
                                                </div>
                                                
                                                {if $server->specifications}
                                                <div class="server-specs">
                                                    <h6>Specifications:</h6>
                                                    <p class="text-muted small">{$server->specifications|nl2br}</p>
                                                </div>
                                                {/if}
                                            </div>
                                            <div class="card-footer">
                                                <small class="text-muted">
                                                    <i class="fa fa-clock-o"></i> 
                                                    Added: {$server->created_at|date_format:"%B %d, %Y"}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                        
                        <div class="row margin-top-20">
                            <div class="col-md-12">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">Infrastructure Summary</h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="summary-stat">
                                                    <h3>{$servers|count}</h3>
                                                    <p>Total Servers</p>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="summary-stat">
                                                    <h3>{assign var="online_count" value=0}{foreach $servers as $server}{if $server->status == 'online'}{assign var="online_count" value=$online_count+1}{/if}{/foreach}{$online_count}</h3>
                                                    <p>Online Servers</p>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="summary-stat">
                                                    <h3>{assign var="locations" value=[]}{foreach $servers as $server}{assign var="locations" value=$locations|array_merge:[$server->location_name]}{/foreach}{$locations|array_unique|count}</h3>
                                                    <p>Locations</p>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="summary-stat">
                                                    <h3>{assign var="total_power" value=0}{foreach $servers as $server}{assign var="total_power" value=$total_power+$server->power_consumption}{/foreach}{$total_power}W</h3>
                                                    <p>Total Power</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    {else}
                        <div class="text-center padding-20">
                            <i class="fa fa-server fa-5x text-muted"></i>
                            <h3 class="text-muted">No Servers Assigned</h3>
                            <p class="text-muted">You don't have any servers assigned to your account yet.</p>
                            <p class="text-muted">Contact support if you believe this is an error.</p>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Client Area Specific Styles */
.dcim-clientarea {
    margin: 20px 0;
}

.dcim-server-card {
    margin-bottom: 20px;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #fff;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 15px 20px;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.card-footer {
    background: #f8f9fa;
    padding: 10px 20px;
    border-top: 1px solid #dee2e6;
}

.server-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f1f1f1;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    color: #495057;
    font-weight: 600;
    min-width: 100px;
}

.detail-row span {
    text-align: right;
    color: #6c757d;
}

.detail-row code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #e83e8c;
}

.server-specs {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f1f1f1;
}

.server-specs h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
}

.summary-stat {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin-bottom: 15px;
}

.summary-stat h3 {
    font-size: 2.5em;
    margin: 0;
    color: #007bff;
    font-weight: bold;
}

.summary-stat p {
    margin: 10px 0 0 0;
    color: #6c757d;
    font-weight: 500;
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.badge-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
    
    .detail-row strong {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .detail-row span {
        text-align: left;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .badge {
        margin-top: 10px;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dcim-server-card {
    animation: fadeInUp 0.6s ease-out;
}

.dcim-server-card:nth-child(2) { animation-delay: 0.1s; }
.dcim-server-card:nth-child(3) { animation-delay: 0.2s; }
.dcim-server-card:nth-child(4) { animation-delay: 0.3s; }
.dcim-server-card:nth-child(5) { animation-delay: 0.4s; }
.dcim-server-card:nth-child(6) { animation-delay: 0.5s; }

/* Status indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online { background-color: #28a745; }
.status-offline { background-color: #6c757d; }
.status-maintenance { background-color: #ffc107; }
.status-provisioning { background-color: #17a2b8; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any client-side functionality here
    
    // Example: Add click handlers for server cards
    const serverCards = document.querySelectorAll('.dcim-server-card .card');
    serverCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Could open a modal with more server details
            console.log('Server card clicked');
        });
    });
    
    // Example: Add tooltips or additional interactions
    const badges = document.querySelectorAll('.badge');
    badges.forEach(function(badge) {
        badge.setAttribute('title', 'Server Status: ' + badge.textContent);
    });
});
</script>