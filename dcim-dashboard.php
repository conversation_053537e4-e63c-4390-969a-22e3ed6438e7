<?php
/**
 * DCIM Dashboard and Rack Visualization Module
 * 
 * This file contains dashboard functionality and rack visualization
 * features for the DCIM addon.
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use WHMCS\Database\Capsule;

/**
 * Dashboard function (legacy - redirects to modern dashboard)
 */
function dcim_dashboard($modulelink) {
    return dcim_modern_dashboard($modulelink);
}

/**
 * Modern Dashboard
 */
function dcim_modern_dashboard($modulelink) {
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using Unicode symbols */
    .fas.fa-server::before { content: "🖥"; }
    .fas.fa-network-wired::before { content: "🌐"; }
    .fas.fa-hdd::before { content: "💾"; }
    .fas.fa-plus-circle::before { content: "➕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-database::before { content: "🗄"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-edit::before { content: "✏"; }
    .fas.fa-eye::before { content: "👁"; }
    .fas.fa-trash::before { content: "🗑"; }
    .fas.fa-magic::before { content: "✨"; }
    .fas.fa-eye-slash::before { content: "🙈"; }
    .fas.fa-folder::before { content: "📁"; }
    .fas.fa-globe::before { content: "🌍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    
    /* Modern DCIM Dashboard Styles */
    /* Modern DCIM Dashboard Styles */
    .dcim-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background: #f8fafc;
        min-height: 100vh;
        margin: 0;
        padding: 0;
    }
    
    .dcim-layout {
        display: flex;
        min-height: 100vh;
    }
    
    .dcim-sidebar {
        width: 280px;
        background: #ffffff;
        border-right: 1px solid #e2e8f0;
        flex-shrink: 0;
        overflow-y: auto;
    }
    
    .dcim-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #f8fafc;
    }
    
    .main-header {
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        padding: 20px 24px;
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    .main-title {
        font-size: 24px;
        font-weight: 700;
        color: #1a202c;
        margin: 0;
    }
    
    .add-location-btn {
        background: #4299e1;
        color: white;
        border: none;
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
        text-decoration: none;
    }
    
    .add-location-btn:hover {
        background: #3182ce;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
    }
    
    .rack-visualization {
        flex: 1;
        padding: 24px;
        overflow: auto;
    }
    
    .page-section-header {
        margin-bottom: 24px;
    }
    
    .page-section-header h3 {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 8px 0;
    }
    
    .racks-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 24px;
    }
    
    .rack-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .rack-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #4299e1;
    }
    
    .rack-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }
    
    .rack-name {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
    }
    
    .rack-type {
        font-size: 14px;
        color: #718096;
        background: #f7fafc;
        padding: 4px 8px;
        border-radius: 6px;
    }
    
    .rack-stats {
        font-size: 14px;
        color: #4a5568;
        line-height: 1.5;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #718096;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #cbd5e0;
    }
    
    .empty-state h3 {
        font-size: 20px;
        font-weight: 600;
        color: #4a5568;
        margin: 0 0 8px 0;
    }
    
    .empty-state p {
        font-size: 16px;
        margin: 0 0 24px 0;
        color: #718096;
    }
    
    .stats-panel {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 32px;
        font-weight: 700;
        color: #4299e1;
        margin-bottom: 4px;
    }
    
    .stat-label {
        font-size: 14px;
        color: #718096;
        font-weight: 500;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .dcim-layout {
            flex-direction: column;
        }
        
        .dcim-sidebar {
            width: 100%;
            border-right: none;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .main-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
        
        .racks-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }
    }
    </style>';
    
    // Get all locations from database with error handling
    try {
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
    } catch (Exception $e) {
        error_log("DCIM: Error fetching locations - " . $e->getMessage());
        $locations = collect([]);
    }
    
    $selected_location = $_GET['location_id'] ?? null;
    
    // Only auto-redirect to rack view if both location_id AND rack_id are explicitly provided
    if ($selected_location && isset($_GET['rack_id'])) {
        return dcim_modern_rack_view($modulelink);
    }
    
    // Output modern interface with proper structure
    echo '<div class="dcim-container">';
    echo '<div class="dcim-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $selected_location);
    
    // Main content
    echo '<div class="dcim-main">';
    echo '<div class="main-header">';
    echo '<div class="main-title">Data Center Infrastructure Management</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showSwitchesTable()">';
    echo '<i class="fas fa-network-wired"></i> All Switches';
    echo '</button>';
    echo '<button class="add-location-btn" onclick="showChassiesTable()">';
    echo '<i class="fas fa-hdd"></i> All Chassies';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    echo '<div class="rack-visualization">';
    
    if (count($locations) > 0) {
        if ($selected_location) {
            // Show racks for selected location
            try {
                $racks = Capsule::table('dcim_racks')->where('location_id', $selected_location)->get();
                $selected_location_name = Capsule::table('dcim_locations')->where('id', $selected_location)->value('name');
            } catch (Exception $e) {
                error_log("DCIM: Error fetching location data - " . $e->getMessage());
                $racks = collect([]);
                $selected_location_name = "Unknown Location";
            }
            
            echo '<div class="page-section-header">';
            echo '<h3>Racks in ' . htmlspecialchars($selected_location_name) . '</h3>';
            echo '</div>';
            
            if (count($racks) > 0) {
                echo '<div class="racks-grid">';
                foreach ($racks as $rack) {
                    $server_count = 0;
                    $used_units = 0;
                    
                    try {
                        $server_count = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->count();
                        $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack->id)->sum('unit_size');
                    } catch (Exception $e) {
                        error_log("DCIM: Error counting servers - " . $e->getMessage());
                    }
                    
                    echo '<div class="rack-card" onclick="selectRack(' . $rack->id . ')">';
                    echo '<div class="rack-card-header">';
                    echo '<div class="rack-name">' . htmlspecialchars($rack->name) . '</div>';
                    echo '<div class="rack-type">' . $rack->units . 'U Rack</div>';
                    echo '</div>';
                    echo '<div class="rack-stats">';
                    echo 'Servers: ' . $server_count . '<br>';
                    echo 'Used: ' . $used_units . '/' . $rack->units . 'U';
                    echo '</div>';
                    echo '</div>';
                }
                echo '</div>';
            } else {
                echo '<div class="empty-state">';
                echo '<i class="fas fa-server"></i>';
                echo '<h3>No Racks Found</h3>';
                echo '<p>No racks found in this location</p>';
                echo '<button class="add-location-btn" onclick="addRack(' . $selected_location . ')" style="margin-top: 16px;">';
                echo '<i class="fas fa-plus"></i> Add First Rack';
                echo '</button>';
                echo '</div>';
            }
        } else {
            echo '<div class="empty-state">';
            echo '<i class="fas fa-map-marker-alt"></i>';
            echo '<h3>Select a Location</h3>';
            echo '<p>Choose a data center location from the sidebar to view its racks and servers</p>';
            
            // Show infrastructure statistics
            try {
                $total_locations = count($locations);
                $total_racks = Capsule::table('dcim_racks')->count();
                $total_servers = Capsule::table('dcim_servers')->count();
                
                if ($total_locations > 0 || $total_racks > 0 || $total_servers > 0) {
                    echo '<div class="stats-panel" style="margin-top: 32px; display: inline-block;">';
                    echo '<div style="font-size: 14px; color: #4a5568; margin-bottom: 16px; font-weight: 600;">Your Infrastructure</div>';
                    echo '<div class="stats-grid">';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_locations . '</div>';
                    echo '<div class="stat-label">Locations</div>';
                    echo '</div>';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_racks . '</div>';
                    echo '<div class="stat-label">Racks</div>';
                    echo '</div>';
                    echo '<div class="stat-item">';
                    echo '<div class="stat-number">' . $total_servers . '</div>';
                    echo '<div class="stat-label">Servers</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                }
            } catch (Exception $e) {
                error_log("DCIM: Error fetching statistics - " . $e->getMessage());
            }
            echo '</div>';
        }
    } else {
        echo '<div class="empty-state">';
        echo '<i class="fas fa-plus-circle"></i>';
        echo '<h3>Welcome to DCIM</h3>';
        echo '<p>Start by adding your first data center location, or load sample data for testing</p>';
        echo '<div style="margin-top: 20px; display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">';
        echo '<button class="add-location-btn" onclick="showAddLocationModal()">';
        echo '<i class="fas fa-plus"></i> Add First Location';
        echo '</button>';
        echo '<button class="add-location-btn" onclick="createSampleData()" style="background: #48bb78;">';
        echo '<i class="fas fa-database"></i> Load Sample Data';
        echo '</button>';
        echo '</div>';
        echo '<p style="font-size: 11px; color: #a0aec0; margin-top: 16px;">Sample data includes 2 locations, 4 racks, and demo servers</p>';
        echo '</div>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript and add page-specific functions
    dcim_generate_sidebar_javascript($modulelink);
    echo '<script>
    function selectRack(rackId) {
        window.location.href = "' . $modulelink . '&action=rack_view&rack_id=" + rackId;
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    function showSwitchesTable() {
        window.location.href = "' . $modulelink . '&action=switches_table";
    }
    
    function showChassiesTable() {
        window.location.href = "' . $modulelink . '&action=chassies_table";
    }
    
    function createSampleData() {
        if (confirm("This will create sample locations, racks, and servers for testing purposes. Continue?")) {
            window.location.href = "' . $modulelink . '&create_sample=true";
        }
    }
    </script>';
}

/**
 * Modern Rack view with visual layout
 */
function dcim_modern_rack_view($modulelink) {
    $rack_id = $_GET['rack_id'];
    
    // Ensure tables exist
    dcim_ensure_tables_exist();
    
    // Load Font Awesome for icons
    echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">';
    
    // Add fallback CSS for when Font Awesome fails to load
    echo '<style>
    /* Font Awesome fallbacks using Unicode symbols */
    .fas.fa-server::before { content: "🖥"; }
    .fas.fa-network-wired::before { content: "🌐"; }
    .fas.fa-hdd::before { content: "💾"; }
    .fas.fa-plus-circle::before { content: "➕"; }
    .fas.fa-plus::before { content: "+"; }
    .fas.fa-database::before { content: "🗄"; }
    .fas.fa-arrow-left::before { content: "←"; }
    .fas.fa-check-circle::before { content: "✓"; }
    .fas.fa-exclamation-circle::before { content: "⚠"; }
    .fas.fa-times::before { content: "✕"; }
    .fas.fa-edit::before { content: "✏"; }
    .fas.fa-eye::before { content: "👁"; }
    .fas.fa-trash::before { content: "🗑"; }
    .fas.fa-magic::before { content: "✨"; }
    .fas.fa-eye-slash::before { content: "🙈"; }
    .fas.fa-folder::before { content: "📁"; }
    .fas.fa-globe::before { content: "🌍"; }
    .fas.fa-th::before { content: "⊞"; }
    .fas.fa-spinner::before { content: "⟳"; }
    .fas.fa-spin::before { animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    
    /* Modern Rack View Styles */
    /* Modern Rack View Styles */
    .rack-view-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background: #f8fafc;
        min-height: 100vh;
        margin: 0;
        padding: 0;
    }
    
    .rack-layout {
        display: flex;
        min-height: 100vh;
    }
    
    .rack-sidebar,
    .dcim-sidebar {
        width: 280px;
        background: #ffffff;
        border-right: 1px solid #e2e8f0;
        flex-shrink: 0;
    }
    
    .rack-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #f8fafc;
    }
    
    .rack-header {
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        padding: 20px 24px;
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    .back-btn {
        background: none;
        border: none;
        color: #718096;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .back-btn:hover {
        background: #f1f5f9;
        color: #4a5568;
    }
    
    .rack-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }
    
    .rack-content {
        flex: 1;
        padding: 24px;
        overflow: auto;
    }
    
    .unassigned-section {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        margin-bottom: 24px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .unassigned-header {
        padding: 20px 24px;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 16px;
        background: #f8fafc;
    }
    
    .unassigned-title {
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .device-filters {
        display: flex;
        gap: 8px;
        margin-left: auto;
    }
    
    .filter-btn {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        color: #4a5568;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .filter-btn.active {
        background: #4299e1;
        color: white;
        border-color: #4299e1;
    }
    
    .unassigned-content {
        padding: 24px;
        text-align: center;
        color: #718096;
        min-height: 120px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        justify-content: center;
        align-items: center;
    }
    
    .rack-visualization {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 32px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .rack-viz-header {
        text-align: center;
        margin-bottom: 32px;
    }
    
    .rack-viz-title {
        font-size: 24px;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 8px 0;
    }
    
    .rack-info {
        font-size: 16px;
        color: #718096;
        margin: 0;
    }
    
    .rack-container {
        display: flex;
        justify-content: center;
        gap: 32px;
        align-items: flex-start;
    }
    
    .rack-visual {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }
    
    .rack-label {
        font-size: 14px;
        font-weight: 600;
        color: #4a5568;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .rack-frame {
        background: #f8fafc;
        border: 3px solid #e2e8f0;
        border-radius: 12px;
        padding: 12px;
        position: relative;
        width: 320px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .rack-unit {
        height: 28px;
        border: 1px solid #cbd5e0;
        margin-bottom: 2px;
        display: flex;
        align-items: center;
        position: relative;
        background: #ffffff;
        border-radius: 4px;
        transition: all 0.2s ease;
    }
    
    .rack-unit:hover {
        border-color: #4299e1;
        background: #ebf8ff;
        transform: scale(1.02);
    }
    
    .rack-unit.occupied {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border-color: #3182ce;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
    }
    
    .rack-unit.occupied.server {
        background: linear-gradient(135deg, #10b981, #059669);
        border-color: #059669;
    }
    
    .rack-unit.occupied.switch {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border-color: #d97706;
    }
    
    .rack-unit.occupied.chassis {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        border-color: #7c3aed;
    }
    
    .unit-number {
        position: absolute;
        left: 8px;
        font-size: 11px;
        font-weight: 600;
        color: #718096;
        min-width: 20px;
    }
    
    .rack-unit.occupied .unit-number {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .device-label {
        flex: 1;
        text-align: center;
        font-size: 12px;
        font-weight: 600;
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .unit-size-indicator {
        position: absolute;
        right: 8px;
        font-size: 10px;
        font-weight: 600;
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 4px;
    }
    
    .rack-stats {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        width: 280px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .stats-title {
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 20px 0;
        text-align: center;
    }
    
    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        font-size: 14px;
    }
    
    .stat-label {
        color: #718096;
        font-weight: 500;
    }
    
    .stat-value {
        font-weight: 600;
        color: #2d3748;
    }
    
    .utilization-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 6px;
    }
    
    .utilization-fill {
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 4px;
    }
    
    .drag-target {
        border: 2px dashed #4299e1;
        background: #ebf8ff;
        transform: scale(1.05);
    }
    
    .unassigned-device {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 12px 16px;
        cursor: grab;
        transition: all 0.2s ease;
        display: inline-block;
        min-width: 120px;
        text-align: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .unassigned-device:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #4299e1;
    }
    
    .unassigned-device.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }
    
    .device-name {
        font-weight: 600;
        color: #2d3748;
        font-size: 13px;
        margin-bottom: 4px;
    }
    
    .device-info {
        font-size: 11px;
        color: #718096;
    }
    
    /* Responsive Design */
    @media (max-width: 1024px) {
        .rack-container {
            flex-direction: column;
            align-items: center;
        }
        
        .rack-stats {
            width: 100%;
            max-width: 400px;
        }
    }
    
    @media (max-width: 768px) {
        .rack-layout {
            flex-direction: column;
        }
        
        .rack-sidebar {
            width: 100%;
            border-right: none;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .rack-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }
        
        .rack-frame {
            width: 280px;
        }
    }
    </style>';
    
    try {
        $rack = Capsule::table('dcim_racks')
            ->join('dcim_locations', 'dcim_racks.location_id', '=', 'dcim_locations.id')
            ->where('dcim_racks.id', $rack_id)
            ->select('dcim_racks.*', 'dcim_locations.name as location_name')
            ->first();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching rack: ' . $e->getMessage() . '</div>';
        return;
    }
    
    if (!$rack) {
        echo '<div class="alert alert-danger">Rack not found!</div>';
        return;
    }
    
    try {
        $servers = Capsule::table('dcim_servers')
            ->where('rack_id', $rack_id)
            ->orderBy('start_unit')
            ->get();
        
        $unassigned_servers = Capsule::table('dcim_servers')
            ->whereNull('rack_id')
            ->get();
        
        $locations = Capsule::table('dcim_locations')->orderBy('name')->get();
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
        $servers = collect([]);
        $unassigned_servers = collect([]);
        $locations = collect([]);
    }
    
    // Add modern rack visualization CSS
    echo '<style>
    .rack-view-container {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        background: #f8fafc !important;
        min-height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .rack-layout {
        display: flex !important;
        min-height: 100vh !important;
    }
    
    .rack-sidebar,
    .dcim-sidebar {
        width: 280px !important;
        background: #ffffff !important;
        border-right: 1px solid #e2e8f0 !important;
        flex-shrink: 0 !important;
    }
    
    .rack-main {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        background: #f8fafc !important;
    }
    
    .rack-header {
        background: #ffffff !important;
        border-bottom: 1px solid #e2e8f0 !important;
        padding: 20px 24px !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .back-btn {
        background: none !important;
        border: none !important;
        color: #718096 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }
    
    .back-btn:hover {
        background: #f1f5f9 !important;
        color: #4a5568 !important;
    }
    
    .rack-title {
        font-size: 20px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
    }
    
    .rack-content {
        flex: 1 !important;
        padding: 24px !important;
        overflow: auto !important;
    }
    
    .unassigned-section {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        margin-bottom: 24px !important;
        overflow: hidden !important;
    }
    
    .unassigned-header {
        padding: 16px 20px !important;
        border-bottom: 1px solid #e2e8f0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .unassigned-title {
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }
    
    .device-filters {
        display: flex !important;
        gap: 8px !important;
        margin-left: auto !important;
    }
    
    .filter-btn {
        background: #f7fafc !important;
        border: 1px solid #e2e8f0 !important;
        color: #4a5568 !important;
        padding: 6px 12px !important;
        border-radius: 6px !important;
        font-size: 13px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }
    
    .filter-btn.active {
        background: #4299e1 !important;
        color: white !important;
        border-color: #4299e1 !important;
    }
    
    .unassigned-content {
        padding: 20px !important;
        text-align: center !important;
        color: #718096 !important;
    }
    
    .rack-visualization {
        background: #ffffff !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 24px !important;
    }
    
    .rack-viz-header {
        text-align: center !important;
        margin-bottom: 24px !important;
    }
    
    .rack-viz-title {
        font-size: 18px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 8px 0 !important;
    }
    
    .rack-info {
        font-size: 14px !important;
        color: #718096 !important;
        margin: 0 !important;
    }
    
    .rack-container {
        display: flex !important;
        justify-content: center !important;
        gap: 24px !important;
    }
    
    .rack-visual {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        gap: 16px !important;
    }
    
    .rack-label {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #4a5568 !important;
    }
    
    .rack-frame {
        background: #f8fafc !important;
        border: 2px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 8px !important;
        position: relative !important;
        width: 300px !important;
    }
    
    .rack-unit {
        height: 24px !important;
        border: 1px solid #cbd5e0 !important;
        margin-bottom: 1px !important;
        display: flex !important;
        align-items: center !important;
        position: relative !important;
        background: #ffffff !important;
        border-radius: 3px !important;
        transition: all 0.2s ease !important;
    }
    
    .rack-unit:hover {
        border-color: #4299e1 !important;
        background: #ebf8ff !important;
    }
    
    .rack-unit.occupied {
        background: linear-gradient(135deg, #4299e1, #3182ce) !important;
        color: white !important;
        border-color: #3182ce !important;
        cursor: pointer !important;
    }
    
    .rack-unit.occupied.server {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        border-color: #059669 !important;
    }
    
    .rack-unit.occupied.switch {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        border-color: #d97706 !important;
    }
    
    .rack-unit.occupied.chassis {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
        border-color: #7c3aed !important;
    }
    
    .unit-number {
        position: absolute !important;
        left: 8px !important;
        font-size: 11px !important;
        font-weight: 600 !important;
        color: #718096 !important;
        min-width: 20px !important;
    }
    
    .rack-unit.occupied .unit-number {
        color: rgba(255, 255, 255, 0.8) !important;
    }
    
    .device-label {
        flex: 1 !important;
        text-align: center !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        padding: 0 8px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }
    
    .unit-size-indicator {
        position: absolute !important;
        right: 8px !important;
        font-size: 10px !important;
        font-weight: 600 !important;
        background: rgba(255, 255, 255, 0.2) !important;
        padding: 2px 4px !important;
        border-radius: 3px !important;
    }
    
    .rack-stats {
        margin-left: 24px !important;
        background: #f8fafc !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        width: 250px !important;
    }

    .stats-title {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 0 16px 0 !important;
    }
    
    .stat-item {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 12px !important;
        font-size: 13px !important;
    }
    
    .stat-label {
        color: #718096 !important;
    }
    
    .stat-value {
        font-weight: 600 !important;
        color: #2d3748 !important;
    }
    
    .utilization-bar {
        width: 100% !important;
        height: 8px !important;
        background: #e2e8f0 !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        margin-top: 4px !important;
    }
    
    .utilization-fill {
        height: 100% !important;
        transition: width 0.3s ease !important;
    }
    
    .drag-target {
        border: 2px dashed #4299e1 !important;
        background: #ebf8ff !important;
    }
    
    .unassigned-device {
        background: #f7fafc !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 6px !important;
        padding: 12px !important;
        margin: 8px !important;
        cursor: grab !important;
        transition: all 0.2s ease !important;
        display: inline-block !important;
        min-width: 120px !important;
        text-align: center !important;
    }
    
    .unassigned-device:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border-color: #cbd5e0 !important;
    }
    
    .unassigned-device.dragging {
        opacity: 0.5 !important;
        transform: rotate(5deg) !important;
    }
    
    .device-name {
        font-weight: 600 !important;
        color: #2d3748 !important;
        font-size: 12px !important;
        margin-bottom: 4px !important;
    }
    
    .device-info {
        font-size: 10px !important;
        color: #718096 !important;
    }
    </style>';
    
    echo '<div class="rack-view-container">';
    echo '<div class="rack-layout">';
    
    // Use shared sidebar
    dcim_generate_sidebar($modulelink, $rack->location_id);
    
    // Main content
    echo '<div class="rack-main">';
    echo '<div class="rack-header">';
    echo '<button class="back-btn" onclick="goBack()">';
    echo '<i class="fas fa-arrow-left"></i>';
    echo '</button>';
    echo '<div class="rack-title">Rack Visualization</div>';
    echo '<div style="margin-left: auto; display: flex; gap: 10px;">';
    echo '<button class="add-location-btn" onclick="showServersTable()">';
    echo '<i class="fas fa-server"></i> All Servers';
    echo '</button>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="rack-content">';
    
    // Unassigned devices section
    echo '<div class="unassigned-section">';
    echo '<div class="unassigned-header">';
    echo '<div class="unassigned-title">';
    echo '<i class="fas fa-info-circle"></i>';
    echo 'Unassigned Devices (' . count($unassigned_servers) . ')';
    echo '</div>';
    echo '<div style="font-size: 12px; color: #718096;">Drag devices to assign or unassign</div>';
    echo '<div class="device-filters">';
    echo '<button class="filter-btn active">All</button>';
    echo '<button class="filter-btn">Servers</button>';
    echo '<button class="filter-btn">Switches</button>';
    echo '<button class="filter-btn">Chassis</button>';
    echo '</div>';
    echo '</div>';
    echo '<div class="unassigned-content">';
    if (count($unassigned_servers) > 0) {
        foreach ($unassigned_servers as $device) {
            echo '<div class="unassigned-device" draggable="true" data-device-id="' . $device->id . '" data-device-size="' . $device->unit_size . '">';
            echo '<div class="device-name">' . htmlspecialchars($device->name) . '</div>';
            echo '<div class="device-info">' . $device->unit_size . 'U</div>';
            echo '</div>';
        }
    } else {
        echo 'No unassigned devices available';
    }
    echo '</div>';
    echo '</div>';
    
    // Rack visualization
    echo '<div class="rack-visualization">';
    echo '<div class="rack-viz-header">';
    echo '<div class="rack-viz-title">' . htmlspecialchars($rack->name) . '</div>';
    echo '<div class="rack-info">' . $rack->units . 'U • ' . htmlspecialchars($rack->location_name) . '</div>';
    echo '</div>';
    
    echo '<div class="rack-container">';
    echo '<div class="rack-visual">';
    echo '<div class="rack-label">Front of Rack</div>';
    echo '<div class="rack-frame" data-rack-id="' . $rack->id . '">';
    
    // Create array to track occupied units
    $occupied_units = [];
    foreach ($servers as $server) {
        if ($server->start_unit) {
            for ($i = $server->start_unit; $i < $server->start_unit + $server->unit_size; $i++) {
                $occupied_units[$i] = $server;
            }
        }
    }
    
    // Draw rack units from top to bottom
    for ($unit = $rack->units; $unit >= 1; $unit--) {
        if (isset($occupied_units[$unit])) {
            $server = $occupied_units[$unit];
            
            // Only draw server label on the first unit of multi-unit servers
            if ($server->start_unit == $unit) {
                $device_type = 'server'; // Default type, could be enhanced to detect type
                echo '<div class="rack-unit occupied ' . $device_type . '" style="height: ' . ($server->unit_size * 25) . 'px;" data-device-id="' . $server->id . '" title="' . htmlspecialchars($server->name) . '">';
                echo '<div class="unit-number">' . $unit . '</div>';
                echo '<div class="device-label">' . htmlspecialchars($server->name) . '</div>';
                echo '<div class="unit-size-indicator">' . $server->unit_size . 'U</div>';
                echo '</div>';
                
                // Skip the additional units for multi-unit devices
                $unit -= ($server->unit_size - 1);
            }
        } else {
            echo '<div class="rack-unit empty" data-unit="' . $unit . '">';
            echo '<div class="unit-number">' . $unit . '</div>';
            echo '</div>';
        }
    }
    
    echo '</div>';
    echo '</div>';
    
    // Rack statistics
    echo '<div class="rack-stats">';
    echo '<div class="stats-title">Rack Statistics</div>';
    
    $used_units = 0;
    $used_power = 0;
    $server_count = count($servers);
    
    try {
        $used_units = Capsule::table('dcim_servers')->where('rack_id', $rack_id)->sum('unit_size');
        $used_power = Capsule::table('dcim_servers')->where('rack_id', $rack_id)->sum('power_consumption');
    } catch (Exception $e) {
        error_log("DCIM: Error calculating stats - " . $e->getMessage());
    }
    
    $unit_utilization = $rack->units > 0 ? ($used_units / $rack->units) * 100 : 0;
    $power_utilization = $rack->power_capacity > 0 ? ($used_power / $rack->power_capacity) * 100 : 0;
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Total Units:</span>';
    echo '<span class="stat-value">' . $rack->units . 'U</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Used Units:</span>';
    echo '<span class="stat-value">' . $used_units . 'U</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Unit Utilization:</span>';
    echo '<span class="stat-value">' . round($unit_utilization, 1) . '%</span>';
    echo '</div>';
    echo '<div class="utilization-bar">';
    echo '<div class="utilization-fill" style="width: ' . $unit_utilization . '%; background: ' . ($unit_utilization > 80 ? '#ef4444' : ($unit_utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
    echo '</div>';
    
    echo '<div class="stat-item" style="margin-top: 16px;">';
    echo '<span class="stat-label">Devices:</span>';
    echo '<span class="stat-value">' . $server_count . '</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Used:</span>';
    echo '<span class="stat-value">' . number_format($used_power) . 'W</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Capacity:</span>';
    echo '<span class="stat-value">' . number_format($rack->power_capacity) . 'W</span>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-label">Power Utilization:</span>';
    echo '<span class="stat-value">' . round($power_utilization, 1) . '%</span>';
    echo '</div>';
    echo '<div class="utilization-bar">';
    echo '<div class="utilization-fill" style="width: ' . $power_utilization . '%; background: ' . ($power_utilization > 80 ? '#ef4444' : ($power_utilization > 60 ? '#f59e0b' : '#10b981')) . ';"></div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
    
    // Use shared sidebar JavaScript
    dcim_generate_sidebar_javascript($modulelink);
    
    // JavaScript for drag and drop functionality
    echo '<script>
    let draggedElement = null;
    
    // Drag and drop functionality
    document.querySelectorAll(".unassigned-device").forEach(device => {
        device.addEventListener("dragstart", function(e) {
            draggedElement = this;
            this.classList.add("dragging");
            e.dataTransfer.setData("text/plain", "");
        });
        
        device.addEventListener("dragend", function() {
            this.classList.remove("dragging");
            draggedElement = null;
        });
    });
    
    // Rack unit drop targets
    document.querySelectorAll(".rack-unit.empty").forEach(unit => {
        unit.addEventListener("dragover", function(e) {
            e.preventDefault();
            this.classList.add("drag-target");
        });
        
        unit.addEventListener("dragleave", function() {
            this.classList.remove("drag-target");
        });
        
        unit.addEventListener("drop", function(e) {
            e.preventDefault();
            this.classList.remove("drag-target");
            
            if (draggedElement) {
                const deviceId = draggedElement.dataset.deviceId;
                const deviceSize = parseInt(draggedElement.dataset.deviceSize);
                const unitNumber = parseInt(this.dataset.unit);
                const rackId = document.querySelector(".rack-frame").dataset.rackId;
                
                // Check if there is enough space
                if (canPlaceDevice(unitNumber, deviceSize)) {
                    assignDevice(deviceId, rackId, unitNumber);
                } else {
                    alert("Not enough consecutive empty units to place this device");
                }
            }
        });
    });
    
    // Occupied devices can be dragged back to unassigned
    document.querySelectorAll(".rack-unit.occupied").forEach(device => {
        device.addEventListener("dragstart", function(e) {
            draggedElement = this;
            e.dataTransfer.setData("text/plain", "");
        });
    });
    
    // Unassigned area as drop target
    document.querySelector(".unassigned-content").addEventListener("dragover", function(e) {
        e.preventDefault();
    });
    
    document.querySelector(".unassigned-content").addEventListener("drop", function(e) {
        e.preventDefault();
        
        if (draggedElement && draggedElement.classList.contains("occupied")) {
            const deviceId = draggedElement.dataset.deviceId;
            unassignDevice(deviceId);
        }
    });
    
    function canPlaceDevice(startUnit, deviceSize) {
        for (let i = 0; i < deviceSize; i++) {
            const unit = document.querySelector(`[data-unit="${startUnit - i}"]`);
            if (!unit || !unit.classList.contains("empty")) {
                return false;
            }
        }
        return true;
    }
    
    function assignDevice(deviceId, rackId, unit) {
        window.location.href = "' . $modulelink . '&action=assign_device&device_id=" + deviceId + "&rack_id=" + rackId + "&unit=" + unit;
    }
    
    function unassignDevice(deviceId) {
        if (confirm("Remove this device from the rack?")) {
            window.location.href = "' . $modulelink . '&action=assign_device&device_id=" + deviceId + "&rack_id=&unit=";
        }
    }
    
    function goBack() {
        window.location.href = "' . $modulelink . '&location_id=' . $rack->location_id . '";
    }
    
    function showServersTable() {
        window.location.href = "' . $modulelink . '&action=servers_table";
    }
    
    // Filter functionality
    document.querySelectorAll(".filter-btn").forEach(btn => {
        btn.addEventListener("click", function() {
            document.querySelectorAll(".filter-btn").forEach(b => b.classList.remove("active"));
            this.classList.add("active");
            
            const filter = this.textContent.toLowerCase();
            // Add filtering logic here if needed
        });
    });
    </script>';
}

/**
 * Dashboard and rack visualization functions
 * Note: dcim_servers_table() is defined in dcim-servers.php to avoid conflicts
 */

?> 