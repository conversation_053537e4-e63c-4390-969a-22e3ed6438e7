<?php
/**
 * DCIM Database Migration Script
 * 
 * This script migrates existing DCIM tables to the new IPAM-compatible schema.
 * Run this if you have existing DCIM tables that need to be updated.
 * 
 * Usage: Access this file directly via browser: /modules/addons/dcim/dcim-migrate-tables.php
 */

if (!defined("WHMCS")) {
    // Allow direct access for debugging
    require_once '../../../init.php';
}

use WHMCS\Database\Capsule;

echo '<h1>DCIM Database Migration Script</h1>';
echo '<p>This script will migrate existing DCIM tables to the new IPAM-compatible schema.</p>';

try {
    // Check if dcim_subnets table exists and get its current structure
    if (Capsule::schema()->hasTable('dcim_subnets')) {
        echo '<h3>Analyzing existing dcim_subnets table...</h3>';
        
        // Get current columns
        $columns = Capsule::schema()->getColumnListing('dcim_subnets');
        echo '<p>Current columns: ' . implode(', ', $columns) . '</p>';
        
        // Check if we need to add the new columns
        $columnsToAdd = [];
        
        if (!in_array('subnet', $columns)) {
            $columnsToAdd[] = 'subnet';
        }
        if (!in_array('ip_version', $columns)) {
            $columnsToAdd[] = 'ip_version';
        }
        if (!in_array('country', $columns)) {
            $columnsToAdd[] = 'country';
        }
        if (!in_array('city', $columns)) {
            $columnsToAdd[] = 'city';
        }
        if (!in_array('is_public', $columns)) {
            $columnsToAdd[] = 'is_public';
        }
        if (!in_array('subnet_type', $columns)) {
            $columnsToAdd[] = 'subnet_type';
        }
        if (!in_array('note', $columns)) {
            $columnsToAdd[] = 'note';
        }
        
        if (!empty($columnsToAdd)) {
            echo '<h3>Adding missing columns...</h3>';
            
            Capsule::schema()->table('dcim_subnets', function ($table) use ($columnsToAdd) {
                if (in_array('subnet', $columnsToAdd)) {
                    $table->string('subnet')->nullable(); // Full CIDR notation
                    echo '<div style="color: green;">✓ Added subnet column</div>';
                }
                if (in_array('ip_version', $columnsToAdd)) {
                    $table->enum('ip_version', ['IPv4', 'IPv6'])->default('IPv4');
                    echo '<div style="color: green;">✓ Added ip_version column</div>';
                }
                if (in_array('country', $columnsToAdd)) {
                    $table->string('country')->nullable();
                    echo '<div style="color: green;">✓ Added country column</div>';
                }
                if (in_array('city', $columnsToAdd)) {
                    $table->string('city')->nullable();
                    echo '<div style="color: green;">✓ Added city column</div>';
                }
                if (in_array('is_public', $columnsToAdd)) {
                    $table->boolean('is_public')->default(false);
                    echo '<div style="color: green;">✓ Added is_public column</div>';
                }
                if (in_array('subnet_type', $columnsToAdd)) {
                    $table->enum('subnet_type', ['Root', 'Customer', 'Management', 'Transit'])->default('Root');
                    echo '<div style="color: green;">✓ Added subnet_type column</div>';
                }
                if (in_array('note', $columnsToAdd)) {
                    $table->text('note')->nullable();
                    echo '<div style="color: green;">✓ Added note column</div>';
                }
            });
            
            // Update existing records to populate the new subnet column
            if (in_array('subnet', $columnsToAdd)) {
                echo '<h3>Migrating existing data...</h3>';
                
                $existingSubnets = Capsule::table('dcim_subnets')->get();
                foreach ($existingSubnets as $subnet) {
                    if (empty($subnet->subnet) && !empty($subnet->network) && !empty($subnet->prefix_length)) {
                        $cidr = $subnet->network . '/' . $subnet->prefix_length;
                        Capsule::table('dcim_subnets')
                            ->where('id', $subnet->id)
                            ->update(['subnet' => $cidr]);
                        echo '<div style="color: blue;">✓ Updated subnet ID ' . $subnet->id . ' with CIDR: ' . $cidr . '</div>';
                    }
                }
            }
            
            // Update status values to match new enum
            echo '<h3>Updating status values...</h3>';
            Capsule::table('dcim_subnets')
                ->where('status', 'active')
                ->update(['status' => 'Available']);
            echo '<div style="color: blue;">✓ Updated status values from "active" to "Available"</div>';
            
        } else {
            echo '<div style="color: blue;">✓ dcim_subnets table is already up to date</div>';
        }
        
    } else {
        echo '<div style="color: red;">✗ dcim_subnets table does not exist</div>';
    }
    
    // Ensure IP addresses table exists with correct structure
    if (!Capsule::schema()->hasTable('dcim_ip_addresses')) {
        echo '<h3>Creating dcim_ip_addresses table...</h3>';
        Capsule::schema()->create('dcim_ip_addresses', function ($table) {
            $table->increments('id');
            $table->integer('subnet_id')->unsigned();
            $table->string('ip_address');
            $table->string('hostname')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['available', 'assigned', 'reserved', 'disabled'])->default('available');
            $table->timestamps();
            
            // Add foreign key to subnets
            $table->foreign('subnet_id')->references('id')->on('dcim_subnets')->onDelete('cascade');
            
            // Ensure each IP is unique within a subnet
            $table->unique(['subnet_id', 'ip_address']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_addresses table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_ip_addresses table already exists</div>';
    }
    
    // Ensure IP assignments table exists
    if (!Capsule::schema()->hasTable('dcim_ip_assignments')) {
        echo '<h3>Creating dcim_ip_assignments table...</h3>';
        Capsule::schema()->create('dcim_ip_assignments', function ($table) {
            $table->increments('id');
            $table->integer('ip_address_id')->unsigned();
            $table->enum('device_type', ['server', 'switch', 'chassis']);
            $table->integer('device_id')->unsigned();
            $table->enum('interface_type', ['management', 'primary', 'secondary', 'ipmi'])->default('primary');
            $table->timestamps();
            
            // Add foreign key to IP addresses
            $table->foreign('ip_address_id')->references('id')->on('dcim_ip_addresses')->onDelete('cascade');
            
            // Ensure one IP can't be assigned multiple times to the same interface
            $table->unique(['ip_address_id', 'device_type', 'device_id', 'interface_type']);
        });
        echo '<div style="color: green;">✓ Created dcim_ip_assignments table</div>';
    } else {
        echo '<div style="color: blue;">✓ dcim_ip_assignments table already exists</div>';
    }
    
    echo '<h2 style="color: green;">✓ Database migration completed successfully!</h2>';
    echo '<p><a href="?m=dcim&action=ipam&subaction=subnets">Go to IPAM Subnets</a></p>';

} catch (Exception $e) {
    echo '<div style="color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; margin: 10px 0;">';
    echo '<h3>Error during migration:</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p>You may need to manually update the database schema or drop and recreate the tables.</p>';
    echo '</div>';
    
    error_log("DCIM: Error in table migration - " . $e->getMessage());
}

echo '<hr>';
echo '<h3>Current dcim_subnets Table Structure:</h3>';

try {
    if (Capsule::schema()->hasTable('dcim_subnets')) {
        $columns = Capsule::schema()->getColumnListing('dcim_subnets');
        echo '<ul>';
        foreach ($columns as $column) {
            echo "<li>$column</li>";
        }
        echo '</ul>';
        
        // Show sample data
        $sampleData = Capsule::table('dcim_subnets')->limit(5)->get();
        if (count($sampleData) > 0) {
            echo '<h4>Sample Data:</h4>';
            echo '<table border="1" style="border-collapse: collapse;">';
            echo '<tr>';
            foreach ($columns as $column) {
                echo "<th>$column</th>";
            }
            echo '</tr>';
            foreach ($sampleData as $row) {
                echo '<tr>';
                foreach ($columns as $column) {
                    echo '<td>' . htmlspecialchars($row->$column ?? '') . '</td>';
                }
                echo '</tr>';
            }
            echo '</table>';
        }
    } else {
        echo '<p style="color: red;">Table does not exist</p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">Error reading table structure: ' . htmlspecialchars($e->getMessage()) . '</p>';
}

echo '<hr>';
echo '<h3>Manual SQL Commands (if needed):</h3>';
echo '<p>If the automatic migration fails, you can run these SQL commands manually:</p>';
echo '<pre style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">';
echo "-- Add missing columns to dcim_subnets table\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN subnet VARCHAR(255) NULL;\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN ip_version ENUM('IPv4', 'IPv6') DEFAULT 'IPv4';\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN country VARCHAR(255) NULL;\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN city VARCHAR(255) NULL;\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN is_public BOOLEAN DEFAULT FALSE;\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN subnet_type ENUM('Root', 'Customer', 'Management', 'Transit') DEFAULT 'Root';\n";
echo "ALTER TABLE dcim_subnets ADD COLUMN note TEXT NULL;\n\n";
echo "-- Update existing data\n";
echo "UPDATE dcim_subnets SET subnet = CONCAT(network, '/', prefix_length) WHERE subnet IS NULL;\n";
echo "UPDATE dcim_subnets SET status = 'Available' WHERE status = 'active';\n";
echo '</pre>';
?>
